@import "tailwindcss";

@theme {
  /* Apple System Font */
  --font-family-sf-pro: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", system-ui, sans-serif;

  /* Apple System Colors */
  --color-apple-blue-light: #007AFF;
  --color-apple-blue-dark: #0A84FF;
  --color-apple-green-light: #34C759;
  --color-apple-green-dark: #30D158;
  --color-apple-orange-light: #FF9500;
  --color-apple-orange-dark: #FF9F0A;
  --color-apple-red-light: #FF3B30;
  --color-apple-red-dark: #FF453A;
  --color-apple-purple-light: #AF52DE;
  --color-apple-purple-dark: #BF5AF2;
  --color-apple-pink-light: #FF2D92;
  --color-apple-pink-dark: #FF375F;
  --color-apple-teal-light: #5AC8FA;
  --color-apple-teal-dark: #64D2FF;
  --color-apple-indigo-light: #5856D6;
  --color-apple-indigo-dark: #5E5CE6;

  /* Apple Gray Scale */
  --color-apple-gray-50: #F2F2F7;
  --color-apple-gray-100: #E5E5EA;
  --color-apple-gray-200: #D1D1D6;
  --color-apple-gray-300: #C7C7CC;
  --color-apple-gray-400: #AEAEB2;
  --color-apple-gray-500: #8E8E93;
  --color-apple-gray-600: #636366;
  --color-apple-gray-700: #48484A;
  --color-apple-gray-800: #3A3A3C;
  --color-apple-gray-900: #2C2C2E;
  --color-apple-gray-950: #1C1C1E;

  /* Apple-style radius */
  --radius: 0.75rem;

  /* Default Light Mode Colors */
  --color-background: #ffffff;
  --color-foreground: #0f172a;
  --color-card: #ffffff;
  --color-card-foreground: #0f172a;
  --color-popover: #ffffff;
  --color-popover-foreground: #0f172a;
  --color-primary: #007AFF;
  --color-primary-foreground: #ffffff;
  --color-secondary: #f1f5f9;
  --color-secondary-foreground: #0f172a;
  --color-muted: #f1f5f9;
  --color-muted-foreground: #64748b;
  --color-accent: #f1f5f9;
  --color-accent-foreground: #0f172a;
  --color-destructive: #FF3B30;
  --color-destructive-foreground: #ffffff;
  --color-border: #e2e8f0;
  --color-input: #e2e8f0;
  --color-ring: #007AFF;
  --color-chart-1: #007AFF;
  --color-chart-2: #34C759;
  --color-chart-3: #FF9500;
  --color-chart-4: #AF52DE;
  --color-chart-5: #FF2D92;
}

.dark {
  /* Dark Mode Colors - Override defaults when .dark class is present */
  --color-background: #1C1C1E;
  --color-foreground: #ffffff;
  --color-card: #2C2C2E;
  --color-card-foreground: #ffffff;
  --color-popover: #2C2C2E;
  --color-popover-foreground: #ffffff;
  --color-primary: #0A84FF;
  --color-primary-foreground: #ffffff;
  --color-secondary: #2C2C2E;
  --color-secondary-foreground: #ffffff;
  --color-muted: #2C2C2E;
  --color-muted-foreground: #8E8E93;
  --color-accent: #2C2C2E;
  --color-accent-foreground: #ffffff;
  --color-destructive: #FF453A;
  --color-destructive-foreground: #ffffff;
  --color-border: #38383A;
  --color-input: #38383A;
  --color-ring: #0A84FF;
  --color-chart-1: #0A84FF;
  --color-chart-2: #30D158;
  --color-chart-3: #FF9F0A;
  --color-chart-4: #BF5AF2;
  --color-chart-5: #FF375F;
}

@layer base {
  * {
    border-color: var(--color-border);
  }
  body {
    background-color: var(--color-background);
    color: var(--color-foreground);
    font-family: var(--font-family-sf-pro);
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
