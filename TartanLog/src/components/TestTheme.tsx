import React from 'react';
import { Moon, Sun, Database, Activity } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function TestTheme() {
  const [theme, setTheme] = React.useState<'light' | 'dark'>('light');

  React.useEffect(() => {
    // 检查本地存储
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | null;
    const initialTheme = savedTheme || 'light';
    setTheme(initialTheme);
    updateTheme(initialTheme);
  }, []);

  const updateTheme = (newTheme: 'light' | 'dark') => {
    const root = window.document.documentElement;
    root.classList.remove('light', 'dark');
    if (newTheme === 'dark') {
      root.classList.add('dark');
    }
    localStorage.setItem('theme', newTheme);
  };

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    updateTheme(newTheme);
  };

  return (
    <div className="min-h-screen bg-background text-foreground transition-colors duration-300">
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        {/* 头部测试 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <Database className="w-6 h-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-foreground">Tartan</h1>
                <p className="text-sm text-muted-foreground">达坦实时Gamma成像系统</p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={toggleTheme}
              className="w-10 h-10 p-0"
            >
              {theme === 'light' ? (
                <Moon className="h-4 w-4" />
              ) : (
                <Sun className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* 颜色测试卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-card border border-border rounded-lg p-6">
            <h2 className="text-lg font-semibold text-foreground mb-4">背景颜色测试</h2>
            <div className="space-y-3">
              <div className="p-3 bg-background border border-border rounded">
                <span className="text-foreground">主背景色 (background)</span>
              </div>
              <div className="p-3 bg-muted rounded">
                <span className="text-muted-foreground">静音背景色 (muted)</span>
              </div>
              <div className="p-3 bg-secondary rounded">
                <span className="text-secondary-foreground">次要背景色 (secondary)</span>
              </div>
            </div>
          </div>

          <div className="bg-card border border-border rounded-lg p-6">
            <h2 className="text-lg font-semibold text-foreground mb-4">按钮测试</h2>
            <div className="space-y-3">
              <Button className="w-full">主要按钮</Button>
              <Button variant="secondary" className="w-full">次要按钮</Button>
              <Button variant="outline" className="w-full">轮廓按钮</Button>
              <Button variant="ghost" className="w-full">幽灵按钮</Button>
              <Button variant="destructive" className="w-full">危险按钮</Button>
            </div>
          </div>
        </div>

        {/* 输入框测试 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h2 className="text-lg font-semibold text-foreground mb-4">输入框测试</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                普通输入框
              </label>
              <input
                type="text"
                placeholder="请输入内容"
                className="w-full px-3 py-2 border border-input bg-background rounded-md text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                选择框
              </label>
              <select className="w-full px-3 py-2 border border-input bg-background rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2">
                <option>选项 1</option>
                <option>选项 2</option>
                <option>选项 3</option>
              </select>
            </div>
          </div>
        </div>

        {/* 状态指示器测试 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h2 className="text-lg font-semibold text-foreground mb-4">状态指示器测试</h2>
          <div className="flex space-x-4">
            <div className="px-3 py-1 rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 flex items-center space-x-1">
              <Activity className="w-3 h-3" />
              <span className="text-xs font-medium">已连接</span>
            </div>
            <div className="px-3 py-1 rounded-full bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400 flex items-center space-x-1">
              <Activity className="w-3 h-3" />
              <span className="text-xs font-medium">未连接</span>
            </div>
            <div className="px-3 py-1 rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 flex items-center space-x-1">
              <Activity className="w-3 h-3" />
              <span className="text-xs font-medium">错误</span>
            </div>
          </div>
        </div>

        {/* 当前主题显示 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h2 className="text-lg font-semibold text-foreground mb-4">当前主题</h2>
          <p className="text-foreground">
            当前主题: <span className="font-mono bg-muted px-2 py-1 rounded">{theme}</span>
          </p>
          <p className="text-muted-foreground text-sm mt-2">
            点击右上角的月亮/太阳图标切换主题
          </p>
        </div>
      </div>
    </div>
  );
}
