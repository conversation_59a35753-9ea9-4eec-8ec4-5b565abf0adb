import React, { useEffect, useState } from 'react';
import { Moon, Sun } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function ThemeToggle() {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');

  useEffect(() => {
    // 检查系统偏好
    const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    // 检查本地存储
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | null;
    
    const initialTheme = savedTheme || (isDark ? 'dark' : 'light');
    setTheme(initialTheme);
    updateTheme(initialTheme);
  }, []);

  const updateTheme = (newTheme: 'light' | 'dark') => {
    const root = window.document.documentElement;
    root.classList.remove('light', 'dark');
    if (newTheme === 'dark') {
      root.classList.add('dark');
    }
    localStorage.setItem('theme', newTheme);
  };

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    updateTheme(newTheme);
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={toggleTheme}
      className="w-9 h-9 p-0 border-[--color-apple-gray-300] dark:border-[--color-apple-gray-600] bg-white dark:bg-[--color-apple-gray-800] hover:bg-[--color-apple-gray-50] dark:hover:bg-[--color-apple-gray-700] transition-colors"
    >
      {theme === 'light' ? (
        <Moon className="h-4 w-4 text-[--color-apple-gray-600] dark:text-[--color-apple-gray-300]" />
      ) : (
        <Sun className="h-4 w-4 text-[--color-apple-gray-600] dark:text-[--color-apple-gray-300]" />
      )}
      <span className="sr-only">切换主题</span>
    </Button>
  );
}
