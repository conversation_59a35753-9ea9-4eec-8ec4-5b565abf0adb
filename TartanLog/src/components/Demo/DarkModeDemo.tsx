import React, { useState } from 'react';
import {
  Card,
  Button,
  Input,
  Select,
  Table,
  Tag,
  Badge,
  Tooltip,
  Message,
  Space,
  Typography,
  Divider,
  Progress,
  Switch,
  Slider,
  Rate,
  DatePicker,
  Upload,
  Tabs,
  Collapse,
  Menu,
  Breadcrumb,
  Steps,
  Empty,
  Result,
  Modal,
  Drawer,
} from 'antd';
import {
  UploadOutlined,
  UserOutlined,
  LaptopOutlined,
  NotificationOutlined,
  HomeOutlined,
  SettingOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { useTheme } from '../../hooks/useTheme';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Step } = Steps;

export const DarkModeDemo: React.FC = () => {
  const { theme, toggleTheme } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);

  // 表格数据
  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: '状态',
      key: 'status',
      render: () => (
        <Tag color="success">正常</Tag>
      ),
    },
  ];

  const dataSource = [
    {
      key: '1',
      name: '张三',
      age: 32,
      address: '北京市朝阳区',
    },
    {
      key: '2',
      name: '李四',
      age: 28,
      address: '上海市浦东新区',
    },
  ];

  return (
    <div className="p-6 space-y-6">
      {/* 标题区域 */}
      <Card>
        <div className="text-center">
          <Title level={2}>Apple风格暗色模式演示</Title>
          <Paragraph>
            本演示页面展示了遵循Apple Human Interface Guidelines的暗色模式设计。
            所有组件都采用了Apple推荐的颜色系统和交互模式。
          </Paragraph>
          <Space>
            <Button type="primary" onClick={toggleTheme}>
              切换到{theme === 'light' ? '暗色' : '亮色'}模式
            </Button>
            <Badge count={5}>
              <Button>通知</Button>
            </Badge>
          </Space>
        </div>
      </Card>

      {/* 基础组件展示 */}
      <Card title="基础组件" extra={<Tag color="blue">Apple风格</Tag>}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 按钮组 */}
          <div>
            <Title level={4}>按钮组件</Title>
            <Space wrap>
              <Button type="primary">主要按钮</Button>
              <Button>默认按钮</Button>
              <Button type="dashed">虚线按钮</Button>
              <Button type="text">文本按钮</Button>
              <Button type="link">链接按钮</Button>
              <Button danger>危险按钮</Button>
            </Space>
          </div>

          <Divider />

          {/* 输入组件 */}
          <div>
            <Title level={4}>输入组件</Title>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Input placeholder="请输入内容" />
              <Select placeholder="请选择选项" style={{ width: 200 }}>
                <Option value="option1">选项1</Option>
                <Option value="option2">选项2</Option>
                <Option value="option3">选项3</Option>
              </Select>
              <DatePicker placeholder="选择日期" />
            </Space>
          </div>

          <Divider />

          {/* 反馈组件 */}
          <div>
            <Title level={4}>反馈组件</Title>
            <Space wrap>
              <Progress percent={70} />
              <Rate defaultValue={4} />
              <Switch defaultChecked />
              <Slider defaultValue={30} style={{ width: 200 }} />
            </Space>
          </div>
        </Space>
      </Card>

      {/* 数据展示组件 */}
      <Card title="数据展示" extra={<Text type="secondary">Apple设计风格</Text>}>
        <Table 
          columns={columns} 
          dataSource={dataSource} 
          pagination={false}
          size="middle"
        />
      </Card>

      {/* 导航组件 */}
      <Card title="导航组件">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 面包屑 */}
          <div>
            <Title level={4}>面包屑导航</Title>
            <Breadcrumb>
              <Breadcrumb.Item href="">
                <HomeOutlined />
              </Breadcrumb.Item>
              <Breadcrumb.Item href="">
                <UserOutlined />
                <span>用户管理</span>
              </Breadcrumb.Item>
              <Breadcrumb.Item>用户列表</Breadcrumb.Item>
            </Breadcrumb>
          </div>

          <Divider />

          {/* 步骤条 */}
          <div>
            <Title level={4}>步骤条</Title>
            <Steps current={1}>
              <Step title="已完成" description="这是一个描述" />
              <Step title="进行中" description="这是一个描述" />
              <Step title="等待中" description="这是一个描述" />
            </Steps>
          </div>

          <Divider />

          {/* 标签页 */}
          <div>
            <Title level={4}>标签页</Title>
            <Tabs defaultActiveKey="1">
              <TabPane tab="标签1" key="1">
                <Text>标签页内容1</Text>
              </TabPane>
              <TabPane tab="标签2" key="2">
                <Text>标签页内容2</Text>
              </TabPane>
              <TabPane tab="标签3" key="3">
                <Text>标签页内容3</Text>
              </TabPane>
            </Tabs>
          </div>
        </Space>
      </Card>

      {/* 其他组件 */}
      <Card title="其他组件">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 折叠面板 */}
          <div>
            <Title level={4}>折叠面板</Title>
            <Collapse defaultActiveKey={['1']}>
              <Panel header="面板标题1" key="1">
                <Text>这是面板内容1</Text>
              </Panel>
              <Panel header="面板标题2" key="2">
                <Text>这是面板内容2</Text>
              </Panel>
            </Collapse>
          </div>

          <Divider />

          {/* 上传组件 */}
          <div>
            <Title level={4}>上传组件</Title>
            <Upload>
              <Button icon={<UploadOutlined />}>点击上传</Button>
            </Upload>
          </div>

          <Divider />

          {/* 空状态 */}
          <div>
            <Title level={4}>空状态</Title>
            <Empty description="暂无数据" />
          </div>

          <Divider />

          {/* 弹窗组件 */}
          <div>
            <Title level={4}>弹窗组件</Title>
            <Space>
              <Button onClick={() => setModalVisible(true)}>打开模态框</Button>
              <Button onClick={() => setDrawerVisible(true)}>打开抽屉</Button>
              <Tooltip title="这是一个工具提示">
                <Button>悬停查看提示</Button>
              </Tooltip>
            </Space>
          </div>
        </Space>
      </Card>

      {/* 模态框 */}
      <Modal
        title="Apple风格模态框"
        visible={modalVisible}
        onOk={() => setModalVisible(false)}
        onCancel={() => setModalVisible(false)}
      >
        <Text>这是一个遵循Apple设计指南的模态框，采用了暗色模式的配色方案。</Text>
      </Modal>

      {/* 抽屉 */}
      <Drawer
        title="Apple风格抽屉"
        placement="right"
        onClose={() => setDrawerVisible(false)}
        visible={drawerVisible}
      >
        <Text>这是一个遵循Apple设计指南的抽屉组件。</Text>
      </Drawer>
    </div>
  );
};
