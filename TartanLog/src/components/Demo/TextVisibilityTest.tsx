import React from 'react';
import {
  Card,
  Typography,
  Space,
  Badge,
  Tag,
  Button,
  Divider,
  Row,
  Col,
} from 'antd';
import { useTheme } from '../../hooks/useTheme';
import { StatusIndicator, ConnectionStatus, DataStatus } from '../Common/StatusIndicator';

const { Title, Text, Paragraph } = Typography;

export const TextVisibilityTest: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <div className="p-6 space-y-6">
      {/* 标题测试 */}
      <Card title="文字可见性测试" extra={<Button onClick={toggleTheme}>切换主题</Button>}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          
          {/* Typography组件测试 */}
          <div>
            <Title level={3}>Typography组件测试</Title>
            <Title level={1}>H1标题 - 应该是白色</Title>
            <Title level={2}>H2标题 - 应该是白色</Title>
            <Title level={3}>H3标题 - 应该是白色</Title>
            <Title level={4}>H4标题 - 应该是白色</Title>
            <Title level={5}>H5标题 - 应该是白色</Title>
            
            <Paragraph>
              这是一个段落文字，在暗色模式下应该是白色的。
              <Text strong>这是加粗文字</Text>，
              <Text type="secondary">这是次要文字</Text>，
              <Text mark>这是标记文字</Text>，
              <Text code>这是代码文字</Text>。
            </Paragraph>
          </div>

          <Divider />

          {/* 状态指示器测试 */}
          <div>
            <Title level={4}>状态指示器测试</Title>
            <Space wrap>
              <StatusIndicator status="success" text="成功状态" />
              <StatusIndicator status="warning" text="警告状态" />
              <StatusIndicator status="error" text="错误状态" />
              <StatusIndicator status="loading" text="加载状态" />
              <StatusIndicator status="default" text="默认状态" />
            </Space>
          </div>

          <Divider />

          {/* 连接状态测试 */}
          <div>
            <Title level={4}>连接状态测试</Title>
            <Space direction="vertical">
              <ConnectionStatus isConnected={true} deviceName="测试设备" />
              <ConnectionStatus isConnected={false} />
              <ConnectionStatus isConnected={false} isConnecting={true} />
            </Space>
          </div>

          <Divider />

          {/* 数据状态测试 */}
          <div>
            <Title level={4}>数据状态测试</Title>
            <Space direction="vertical">
              <DataStatus isReceiving={true} dataCount={150} lastUpdateTime={new Date()} />
              <DataStatus isReceiving={false} dataCount={75} lastUpdateTime={new Date()} />
              <DataStatus isReceiving={false} dataCount={0} />
            </Space>
          </div>

          <Divider />

          {/* Badge和Tag测试 */}
          <div>
            <Title level={4}>Badge和Tag测试</Title>
            <Space wrap>
              <Badge status="success" text="成功状态" />
              <Badge status="error" text="错误状态" />
              <Badge status="warning" text="警告状态" />
              <Badge status="processing" text="处理中" />
              <Badge status="default" text="默认状态" />
            </Space>
            <br />
            <br />
            <Space wrap>
              <Tag color="blue">蓝色标签</Tag>
              <Tag color="green">绿色标签</Tag>
              <Tag color="orange">橙色标签</Tag>
              <Tag color="red">红色标签</Tag>
              <Tag>默认标签</Tag>
            </Space>
          </div>

          <Divider />

          {/* 模拟面板状态文字 */}
          <div>
            <Title level={4}>面板状态文字测试</Title>
            <Row gutter={16}>
              <Col span={8}>
                <Card title="Job设置状态" size="small">
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-md p-3">
                    <Space direction="vertical" size="small" className="w-full">
                      <div className="flex justify-between">
                        <span className="text-slate-600 dark:text-white">当前井号:</span>
                        <strong className="text-slate-800 dark:text-white">TEST-001</strong>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-600 dark:text-white">连接状态:</span>
                        <strong className="text-slate-800 dark:text-white">已连接</strong>
                      </div>
                    </Space>
                  </div>
                </Card>
              </Col>
              
              <Col span={8}>
                <Card title="Gamma设置状态" size="small">
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-md p-3">
                    <Space direction="vertical" size="small" className="w-full">
                      <div className="flex justify-between">
                        <span className="text-slate-600 dark:text-white">当前GR数量:</span>
                        <strong className="text-slate-800 dark:text-white">4</strong>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-600 dark:text-white">曲线平滑:</span>
                        <strong className="text-slate-800 dark:text-white">已启用</strong>
                      </div>
                    </Space>
                  </div>
                </Card>
              </Col>
              
              <Col span={8}>
                <Card title="成像设置状态" size="small">
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-md p-3">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-4">
                        <span className="text-slate-600 dark:text-white">
                          当前模式: <strong className="text-slate-800 dark:text-white">4扇区</strong>
                        </span>
                        <span className="text-slate-600 dark:text-white">
                          方向配置: <strong className="text-slate-800 dark:text-white">4个方向</strong>
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-slate-500 dark:text-white">就绪</span>
                      </div>
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>
          </div>

          <Divider />

          {/* 普通HTML元素测试 */}
          <div>
            <Title level={4}>普通HTML元素测试</Title>
            <div>
              <h1>HTML H1标题</h1>
              <h2>HTML H2标题</h2>
              <h3>HTML H3标题</h3>
              <p>这是一个普通的p标签段落，应该在暗色模式下显示为白色。</p>
              <span>这是一个span元素</span>
              <br />
              <strong>这是strong元素</strong>
              <br />
              <b>这是b元素</b>
              <br />
              <div>这是一个div元素中的文字</div>
            </div>
          </div>

          {/* 当前主题信息 */}
          <div className="mt-8 p-4 border rounded-lg">
            <Text strong>当前主题: {theme === 'dark' ? '暗色模式' : '亮色模式'}</Text>
            <br />
            <Text type="secondary">
              在暗色模式下，所有文字都应该清晰可见，使用Apple推荐的白色(#FFFFFF)作为主要文字颜色。
            </Text>
          </div>
        </Space>
      </Card>
    </div>
  );
};
