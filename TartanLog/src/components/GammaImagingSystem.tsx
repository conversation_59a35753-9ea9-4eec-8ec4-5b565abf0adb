"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Play, Square, Upload, Activity, Moon, Sun } from "lucide-react"

interface GammaData {
  path: string
  depth: number
  label: string
  value: number
  time: string
  scale: number
  offset: number
  length: number
  result: string
}

export default function GammaImagingSystem() {
  const [theme, setTheme] = useState<"light" | "dark">("light")
  const [isConnected, setIsConnected] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  const [jobId, setJobId] = useState("")
  const [wellNumber, setWellNumber] = useState("")
  const [refreshInterval, setRefreshInterval] = useState(60)
  const [smoothCurve, setSmoothCurve] = useState(false)
  const [gammaData, setGammaData] = useState<GammaData[]>([])
  const [imagingSettings, setImagingSettings] = useState({
    angle: 0,
    left: 0,
    right: 0,
    up: 0,
    down: 0,
  })

  // 主题切换逻辑
  useEffect(() => {
    const savedTheme = localStorage.getItem("theme") as "light" | "dark" | null
    const initialTheme = savedTheme || "light"
    setTheme(initialTheme)
    updateTheme(initialTheme)
  }, [])

  const updateTheme = (newTheme: "light" | "dark") => {
    const root = window.document.documentElement
    root.classList.remove("light", "dark")
    if (newTheme === "dark") {
      root.classList.add("dark")
    }
    localStorage.setItem("theme", newTheme)
  }

  const toggleTheme = () => {
    const newTheme = theme === "light" ? "dark" : "light"
    setTheme(newTheme)
    updateTheme(newTheme)
  }

  // 模拟实时数据更新
  useEffect(() => {
    if (isRunning) {
      const interval = setInterval(() => {
        const newData: GammaData = {
          path: `路径${Math.floor(Math.random() * 100)}`,
          depth: Math.random() * 1000,
          label: `标签${Math.floor(Math.random() * 10)}`,
          value: Math.random() * 100,
          time: new Date().toLocaleTimeString(),
          scale: Math.random() * 10,
          offset: Math.random() * 5,
          length: Math.random() * 50,
          result: Math.random() > 0.5 ? "正常" : "异常",
        }
        setGammaData((prev) => [newData, ...prev.slice(0, 9)])
      }, refreshInterval * 1000)

      return () => clearInterval(interval)
    }
  }, [isRunning, refreshInterval])

  const handleConnect = () => {
    setIsConnected(!isConnected)
  }

  const handleStartStop = () => {
    setIsRunning(!isRunning)
  }

  const handleExport = () => {
    console.log("导出数据")
  }

  const handleClearData = () => {
    setGammaData([])
  }

  return (
    <div className="h-screen bg-background flex flex-col overflow-hidden">
      {/* 头部 - 紧凑设计 */}
      <div className="flex items-center justify-between bg-card border-b border-border px-4 py-2">
        <div className="flex items-center space-x-4">
          <div className="text-2xl font-bold text-blue-600">Tartan</div>
          <div className="text-lg font-medium text-foreground">达坦实时Gamma成像系统</div>
          <div className="text-sm text-muted-foreground">V1.0.0.17</div>
          <div
            className={`px-2 py-1 rounded text-xs font-medium ${
              isConnected
                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                : "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400"
            }`}
          >
            {isConnected ? "已连接" : "未连接"}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={toggleTheme} className="w-8 h-8 p-0 bg-transparent">
            {theme === "light" ? <Moon className="h-3 w-3" /> : <Sun className="h-3 w-3" />}
          </Button>
          <Button size="sm" variant="outline">
            设置
          </Button>
          <Button size="sm" variant="outline">
            导出
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧控制面板 - 紧凑布局 */}
        <div className="w-80 bg-card border-r border-border p-3 space-y-3 overflow-y-auto">
          {/* Job设置 */}
          <div className="border border-border rounded p-3">
            <div className="text-sm font-medium mb-2">Job设置</div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label className="w-12 text-xs">JobId</Label>
                <Input
                  value={jobId}
                  onChange={(e) => setJobId(e.target.value)}
                  className="flex-1 h-7 text-xs"
                  placeholder="输入Job ID"
                />
                <Button size="sm" variant="outline" className="h-7 px-2 bg-transparent">
                  <Upload className="w-3 h-3" />
                </Button>
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              </div>

              <div className="flex items-center space-x-2">
                <Label className="w-12 text-xs">井号</Label>
                <Input
                  value={wellNumber}
                  onChange={(e) => setWellNumber(e.target.value)}
                  className="flex-1 h-7 text-xs"
                  placeholder="输入井号"
                />
                <Select defaultValue="趟钻">
                  <SelectTrigger className="w-20 h-7 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="趟钻">趟钻</SelectItem>
                    <SelectItem value="其他">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex space-x-1 pt-2">
                <Button
                  onClick={handleConnect}
                  size="sm"
                  variant={isConnected ? "destructive" : "default"}
                  className="flex-1 h-7 text-xs"
                >
                  {isConnected ? "断开" : "连接"}
                </Button>
                <Button size="sm" variant="outline" className="flex-1 h-7 text-xs bg-transparent">
                  数据传输
                </Button>
                <Button size="sm" variant="outline" className="flex-1 h-7 text-xs bg-transparent">
                  接收
                </Button>
              </div>
            </div>
          </div>

          {/* Gamma设置 */}
          <div className="border border-border rounded p-3">
            <div className="text-sm font-medium mb-2">Gamma设置</div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Select defaultValue="">
                  <SelectTrigger className="flex-1 h-7 text-xs">
                    <SelectValue placeholder="选择配置" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="config1">配置1</SelectItem>
                    <SelectItem value="config2">配置2</SelectItem>
                  </SelectContent>
                </Select>
                <Button size="sm" className="h-7 px-2 text-xs">
                  添加
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="smoothCurve"
                    checked={smoothCurve}
                    onCheckedChange={(checked) => setSmoothCurve(checked as boolean)}
                  />
                  <Label htmlFor="smoothCurve" className="text-xs">
                    曲线平滑
                  </Label>
                </div>
                <Button
                  onClick={handleClearData}
                  size="sm"
                  variant="outline"
                  className="h-7 px-2 text-xs bg-transparent"
                >
                  清空
                </Button>
              </div>

              <div className="flex items-center space-x-2">
                <Label className="text-xs">刷新间隔(秒)</Label>
                <Input
                  type="number"
                  value={refreshInterval}
                  onChange={(e) => setRefreshInterval(Number(e.target.value))}
                  className="w-16 h-7 text-xs"
                />
                <Button size="sm" className="h-7 px-2 text-xs">
                  确定
                </Button>
              </div>
            </div>
          </div>

          {/* 成像设置 */}
          <div className="border border-border rounded p-3">
            <div className="text-sm font-medium mb-2">成像设置</div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label className="w-8 text-xs">扇区</Label>
                <Input
                  type="number"
                  value={imagingSettings.angle}
                  onChange={(e) =>
                    setImagingSettings((prev) => ({
                      ...prev,
                      angle: Number(e.target.value),
                    }))
                  }
                  className="w-16 h-7 text-xs"
                  placeholder="0"
                />
              </div>

              {/* 方向控制 - 超紧凑布局 */}
              <div className="flex items-center space-x-1">
                <Label className="text-xs w-6">上</Label>
                <Input
                  type="number"
                  value={imagingSettings.up}
                  onChange={(e) =>
                    setImagingSettings((prev) => ({
                      ...prev,
                      up: Number(e.target.value),
                    }))
                  }
                  className="w-10 h-6 text-xs"
                  placeholder="0"
                />
                <Label className="text-xs w-6">下</Label>
                <Input
                  type="number"
                  value={imagingSettings.down}
                  onChange={(e) =>
                    setImagingSettings((prev) => ({
                      ...prev,
                      down: Number(e.target.value),
                    }))
                  }
                  className="w-10 h-6 text-xs"
                  placeholder="0"
                />
              </div>

              <div className="flex items-center space-x-1">
                <Label className="text-xs w-6">左</Label>
                <Input
                  type="number"
                  value={imagingSettings.left}
                  onChange={(e) =>
                    setImagingSettings((prev) => ({
                      ...prev,
                      left: Number(e.target.value),
                    }))
                  }
                  className="w-10 h-6 text-xs"
                  placeholder="0"
                />
                <Label className="text-xs w-6">右</Label>
                <Input
                  type="number"
                  value={imagingSettings.right}
                  onChange={(e) =>
                    setImagingSettings((prev) => ({
                      ...prev,
                      right: Number(e.target.value),
                    }))
                  }
                  className="w-10 h-6 text-xs"
                  placeholder="0"
                />
              </div>

              <Button size="sm" className="w-full h-6 text-xs">
                确定
              </Button>
            </div>
          </div>
        </div>

        {/* 右侧显示区域 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* 实时Gamma曲线 */}
          <div className="flex-1 bg-card border-b border-border">
            <div className="flex items-center justify-between bg-green-50 dark:bg-green-800/20 px-4 py-2 border-b border-border">
              <div className="flex items-center space-x-2">
                <div className="text-sm font-medium text-green-700 dark:text-green-300">实时Gamma曲线</div>
                <div className="text-xs text-green-600 dark:text-green-400">井深 m</div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  onClick={handleStartStop}
                  size="sm"
                  variant={isRunning ? "destructive" : "default"}
                  className="h-7 text-xs"
                >
                  {isRunning ? (
                    <>
                      <Square className="w-3 h-3 mr-1" />
                      停止
                    </>
                  ) : (
                    <>
                      <Play className="w-3 h-3 mr-1" />
                      开始
                    </>
                  )}
                </Button>
                <Button onClick={handleExport} size="sm" variant="outline" className="h-7 text-xs bg-transparent">
                  <Upload className="w-3 h-3 mr-1" />
                  导出
                </Button>
              </div>
            </div>
            <div className="flex-1 bg-white dark:bg-gray-900 flex items-center justify-center">
              <div className="text-center text-muted-foreground">
                <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">{isRunning ? "正在采集数据..." : "点击开始按钮开始数据采集"}</p>
              </div>
            </div>
          </div>

          {/* 数据表格 */}
          <div className="h-48 bg-card overflow-hidden">
            <div className="h-full overflow-auto">
              <Table>
                <TableHeader className="sticky top-0 bg-background">
                  <TableRow className="text-xs">
                    <TableHead className="h-8 px-2">路径</TableHead>
                    <TableHead className="h-8 px-2">源深</TableHead>
                    <TableHead className="h-8 px-2">标签</TableHead>
                    <TableHead className="h-8 px-2">数值</TableHead>
                    <TableHead className="h-8 px-2">时间</TableHead>
                    <TableHead className="h-8 px-2">Scale</TableHead>
                    <TableHead className="h-8 px-2">Offset</TableHead>
                    <TableHead className="h-8 px-2">套长</TableHead>
                    <TableHead className="h-8 px-2">结果</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {gammaData.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center text-gray-500 py-4 text-xs">
                        暂无数据
                      </TableCell>
                    </TableRow>
                  ) : (
                    gammaData.map((data, index) => (
                      <TableRow key={index} className="text-xs">
                        <TableCell className="px-2 py-1">{data.path}</TableCell>
                        <TableCell className="px-2 py-1">{data.depth.toFixed(2)}</TableCell>
                        <TableCell className="px-2 py-1">{data.label}</TableCell>
                        <TableCell className="px-2 py-1">{data.value.toFixed(2)}</TableCell>
                        <TableCell className="px-2 py-1">{data.time}</TableCell>
                        <TableCell className="px-2 py-1">{data.scale.toFixed(2)}</TableCell>
                        <TableCell className="px-2 py-1">{data.offset.toFixed(2)}</TableCell>
                        <TableCell className="px-2 py-1">{data.length.toFixed(2)}</TableCell>
                        <TableCell className="px-2 py-1">
                          <Badge variant={data.result === "正常" ? "default" : "destructive"} className="text-xs">
                            {data.result}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
