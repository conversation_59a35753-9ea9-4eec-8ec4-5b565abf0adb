import React from 'react';
import { Card, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { useTheme } from '../../hooks/useTheme';
import { cn } from '@/lib/utils';

interface ModernCardProps {
  gradient?: boolean;
  glassEffect?: boolean;
  children: React.ReactNode;
  title?: React.ReactNode;
  className?: string;
}

export const ModernCard: React.FC<ModernCardProps> = ({
  gradient = false,
  glassEffect = false,
  className = '',
  title,
  children,
}) => {
  const { theme } = useTheme();

  const getCardStyles = () => {
    let styles = 'transition-all duration-300 hover:shadow-xl rounded-2xl';

    if (gradient) {
      if (theme === 'light') {
        styles += ' bg-gradient-to-br from-white to-slate-50';
      } else {
        // 苹果风格渐变
        styles += ' bg-gradient-to-br from-[#1C1C1E] to-[#2C2C2E]';
      }
    }

    if (glassEffect) {
      if (theme === 'light') {
        styles += ' backdrop-blur-sm bg-white/80 border border-slate-200/50';
      } else {
        // 苹果风格玻璃效果
        styles += ' backdrop-blur-sm bg-[#1C1C1E]/80 border border-[#38383A]/50';
      }
    }

    return styles;
  };

  return (
    <Card className={cn(getCardStyles(), className)}>
      {title && (
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
      )}
      <CardContent className={title ? '' : 'pt-6'}>
        {children}
      </CardContent>
    </Card>
  );
};
