import React, { useState } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Space,
  Badge,
  Typography,
  Row,
  Col,
  Divider,
  message
} from 'antd';
import {
  LinkOutlined,
  DisconnectOutlined,
  CloudUploadOutlined,
  InboxOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import { useAppStore } from '../../store/useAppStore';
import { ModernCard } from '../Common/ModernCard';

const { Text } = Typography;
const { Option } = Select;

interface JobSettingsPanelProps {
  className?: string;
}

export const JobSettingsPanel: React.FC<JobSettingsPanelProps> = ({ className }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 使用状态管理
  const {
    connection,
    jobSettings,
    setJobSettings,
    connect,
    disconnect,
    exportData,
    startDataTransfer,
    startNetListen
  } = useAppStore();

  // 模拟数据
  const runOptions = [
    { value: '1', label: '第1趟' },
    { value: '2', label: '第2趟' },
    { value: '3', label: '第3趟' },
  ];

  const handleConnect = async () => {
    setLoading(true);
    try {
      if (connection.isConnected) {
        await disconnect();
        message.success('设备已断开');
      } else {
        await connect();
        message.success('设备连接成功');
      }
    } catch (error) {
      message.error(connection.isConnected ? '断开失败' : '连接失败');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      await exportData();
      message.success('数据导出成功');
    } catch (error) {
      message.error('数据导出失败');
    }
  };

  const handleDataTransfer = async () => {
    if (!connection.isConnected) {
      message.warning('请先连接设备');
      return;
    }
    try {
      await startDataTransfer();
      message.success('数据远传已启动');
    } catch (error) {
      message.error('数据远传启动失败');
    }
  };

  const handleNetListen = async () => {
    try {
      await startNetListen();
      message.success('网络接收已启动');
    } catch (error) {
      message.error('网络接收启动失败');
    }
  };

  // 表单值变化时更新状态
  const handleFormChange = (changedValues: any) => {
    setJobSettings(changedValues);
  };

  return (
    <ModernCard
      title={
        <div className="flex items-center justify-between">
          <span className="text-slate-800 dark:text-white font-semibold">Job 设置</span>
          <Badge
            status={connection.isConnected ? "success" : "error"}
            text={connection.isConnected ? "已连接" : "未连接"}
          />
        </div>
      }
      className={`${className}`}
      gradient={true}
      styles={{ body: { padding: '20px' } }}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={jobSettings}
        onValuesChange={handleFormChange}
      >
        {/* 井号显示 - 只有在有井号时才显示 */}
        {jobSettings.wellName && (
          <div className="mb-4 text-right">
            <Text className="text-sm text-slate-600 dark:text-white">
              当前井号: {jobSettings.wellName}
            </Text>
          </div>
        )}

        {/* 第一行：JobId和导出 */}
        <Row gutter={16} className="mb-4">
          <Col span={8}>
            <Form.Item
              label="JobId"
              name="jobId"
              rules={[{ required: true, message: '请输入JobId' }]}
            >
              <Input 
                placeholder="输入JobId" 
                className="text-center"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label=" " className="mb-0">
              <Button 
                icon={<ExportOutlined />}
                onClick={handleExport}
                className="w-full"
              >
                导出
              </Button>
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item label=" " className="mb-0">
              <div className="flex justify-center items-center h-8">
                <div
                  className={`w-3 h-3 rounded-full ${
                    connection.isConnected ? 'bg-green-500' : 'bg-red-500'
                  }`}
                />
              </div>
            </Form.Item>
          </Col>
        </Row>

        {/* 第二行：井号和趟钻 */}
        <Row gutter={16} className="mb-4">
          <Col span={12}>
            <Form.Item
              label="井号"
              name="wellName"
            >
              <Input
                placeholder="请输入井号"
                className="text-center"
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="趟钻"
              name="run"
            >
              <Select placeholder="请选择趟钻">
                {runOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Divider className="my-4" />

        {/* 操作按钮组 */}
        <Space size="middle" wrap className="w-full justify-center">
          <Button
            type={connection.isConnected ? "default" : "primary"}
            icon={connection.isConnected ? <DisconnectOutlined /> : <LinkOutlined />}
            loading={loading}
            onClick={handleConnect}
            className="min-w-[80px] font-medium shadow-sm hover:shadow-md transition-all duration-200"
            size="middle"
          >
            {connection.isConnected ? "断开" : "连接"}
          </Button>

          <Button
            icon={<CloudUploadOutlined />}
            onClick={handleDataTransfer}
            disabled={!connection.isConnected}
            className="min-w-[100px] font-medium shadow-sm hover:shadow-md transition-all duration-200"
            size="middle"
          >
            数据远传
          </Button>

          <Button
            icon={<InboxOutlined />}
            onClick={handleNetListen}
            className="min-w-[80px] font-medium shadow-sm hover:shadow-md transition-all duration-200"
            size="middle"
          >
            接收
          </Button>
        </Space>
      </Form>
    </ModernCard>
  );
};
