import React from 'react';
import {
  Form,
  Select,
  Button,
  Typography,
  Row,
  Col,
  Space,
  Divider,
  Badge
} from 'antd';
import { ModernCard } from '../Common/ModernCard';
import {
  CheckOutlined,
  SettingOutlined,
  AimOutlined,
} from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;

interface ImagingSettingsPanelProps {
  className?: string;
}

export const ImagingSettingsPanel: React.FC<ImagingSettingsPanelProps> = ({ className }) => {
  const [form] = Form.useForm();

  // 扇区选项
  const sectorOptions = [
    { value: '0', label: '无扇区', color: 'default' },
    { value: '2', label: '2扇区', color: 'blue' },
    { value: '4', label: '4扇区', color: 'green' },
  ];

  // 方向选项 - 简化为更直观的选项
  const directionOptions = [
    { value: '0', label: '0°' },
    { value: '90', label: '90°' },
    { value: '180', label: '180°' },
    { value: '270', label: '270°' },
  ];

  const handleConfirm = () => {
    form.validateFields().then(values => {
      console.log('成像设置:', values);
      // 这里后续会调用后端API更新成像设置
    });
  };

  const handleSectorChange = (value: string) => {
    console.log('扇区变更:', value);
    // 这里可以根据扇区数量动态调整方向选项
  };

  const currentSector = form.getFieldValue('sector') || '';
  const sectorInfo = sectorOptions.find(opt => opt.value === currentSector) || { label: '未选择', color: 'default' };

  return (
    <ModernCard
      title={
        <div className="flex items-center space-x-2">
          <AimOutlined className="text-blue-600 dark:text-blue-400" />
          <span className="text-slate-800 dark:text-white font-semibold">成像设置</span>
          <Badge
            color={sectorInfo?.color}
            text={sectorInfo?.label}
            className="ml-2"
          />
        </div>
      }
      className={className}
      gradient={true}
      styles={{ body: { padding: '16px' } }}
      size="small"
    >
      <Form
        form={form}
        layout="inline"
        initialValues={{
          sector: '',
          left: '',
          right: '',
          up: '',
          down: '',
        }}
        className="w-full"
      >
        <Row gutter={[16, 16]} className="w-full">
          {/* 第一行：扇区选择 */}
          <Col span={24}>
            <div className="flex items-center justify-between">
              <Form.Item
                label="扇区模式"
                name="sector"
                className="mb-0"
              >
                <Select
                  onChange={handleSectorChange}
                  className="w-32"
                  size="small"
                  placeholder="请选择扇区"
                >
                  {sectorOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Button
                type="primary"
                icon={<CheckOutlined />}
                onClick={handleConfirm}
                size="small"
              >
                应用设置
              </Button>
            </div>
          </Col>

          <Col span={24}>
            <Divider className="my-2" />
          </Col>

          {/* 第二行：方向设置 - 紧凑布局 */}
          <Col span={24}>
            <div className="grid grid-cols-2 gap-4">
              {/* 左侧：上下方向 */}
              <div className="space-y-3">
                <Form.Item
                  label="上方向"
                  name="up"
                  className="mb-0"
                >
                  <Select size="small" className="w-full" placeholder="选择角度">
                    {directionOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  label="下方向"
                  name="down"
                  className="mb-0"
                >
                  <Select size="small" className="w-full" placeholder="选择角度">
                    {directionOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </div>

              {/* 右侧：左右方向 */}
              <div className="space-y-3">
                <Form.Item
                  label="左方向"
                  name="left"
                  className="mb-0"
                >
                  <Select size="small" className="w-full" placeholder="选择角度">
                    {directionOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  label="右方向"
                  name="right"
                  className="mb-0"
                >
                  <Select size="small" className="w-full" placeholder="选择角度">
                    {directionOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </div>
            </div>
          </Col>

          {/* 第三行：状态信息 */}
          <Col span={24}>
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-md p-3">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-4">
                  <span className="text-slate-600 dark:text-white">
                    当前模式: <strong className="text-slate-800 dark:text-white">{sectorInfo?.label || '未选择'}</strong>
                  </span>
                  <span className="text-slate-600 dark:text-white">
                    方向配置: <strong className="text-slate-800 dark:text-white">4个方向</strong>
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-slate-500 dark:text-white">就绪</span>
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </Form>
    </ModernCard>
  );
};
