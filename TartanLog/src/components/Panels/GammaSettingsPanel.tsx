import React, { useState } from 'react';
import {
  Form,
  Select,
  Button,
  Switch,
  InputNumber,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Divider
} from 'antd';
import { ModernCard } from '../Common/ModernCard';
import {
  PlusOutlined,
  ClearOutlined,
  CheckOutlined,
} from '@ant-design/icons';

const { Text } = Typography;
const { Option } = Select;

interface GammaSettingsPanelProps {
  className?: string;
}

export const GammaSettingsPanel: React.FC<GammaSettingsPanelProps> = ({ className }) => {
  const [form] = Form.useForm();
  const [selectedGRs, setSelectedGRs] = useState<string[]>([]);
  const [smoothEnabled, setSmoothEnabled] = useState(false);

  // 可用的GR选项
  const grOptions = [
    { value: 'GV1', label: 'GV1' },
    { value: 'GV2', label: 'GV2' },
    { value: 'GV3', label: 'GV3' },
    { value: 'GV4', label: 'GV4' },
    { value: 'GV5', label: 'GV5' },
    { value: 'GV6', label: 'GV6' },
  ];

  const handleAddGR = () => {
    const selectedGR = form.getFieldValue('selectedGR');
    if (selectedGR && !selectedGRs.includes(selectedGR)) {
      setSelectedGRs([...selectedGRs, selectedGR]);
      form.setFieldValue('selectedGR', undefined);
    }
  };

  const handleRemoveGR = (grToRemove: string) => {
    setSelectedGRs(selectedGRs.filter(gr => gr !== grToRemove));
  };

  const handleClearAll = () => {
    setSelectedGRs([]);
  };

  const handleConfirmInterval = () => {
    const interval = form.getFieldValue('refreshInterval');
    console.log('设置刷新间隔:', interval);
    // 这里后续会调用后端API
  };

  return (
    <ModernCard
      title={<span className="text-slate-800 dark:text-white font-semibold">Gamma 设置</span>}
      className={className}
      gradient={true}
      styles={{ body: { padding: '20px' } }}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          refreshInterval: 5,
          selectedGR: undefined,
        }}
      >
        {/* GR选择和添加 */}
        <Row gutter={16} className="mb-4">
          <Col span={8}>
            <Form.Item label="GR类型" name="selectedGR">
              <Select placeholder="请选择GR类型">
                {grOptions
                  .filter(option => !selectedGRs.includes(option.value))
                  .map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label=" " className="mb-0">
              <Button 
                icon={<PlusOutlined />}
                onClick={handleAddGR}
                className="w-full"
              >
                添加
              </Button>
            </Form.Item>
          </Col>
          <Col span={10}>
            <Form.Item label=" " className="mb-0">
              <div className="flex items-center h-8">
                <Switch
                  checked={smoothEnabled}
                  onChange={setSmoothEnabled}
                  className="mr-2"
                />
                <Text className="text-slate-700 dark:text-white">曲线平滑</Text>
              </div>
            </Form.Item>
          </Col>
        </Row>

        {/* 已选择的GR列表 */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <Text strong>已选择的GR:</Text>
            <Button 
              size="small" 
              icon={<ClearOutlined />}
              onClick={handleClearAll}
              disabled={selectedGRs.length === 0}
            >
              清空
            </Button>
          </div>
          <div className="min-h-[40px] p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border">
            {selectedGRs.length > 0 ? (
              <Space wrap>
                {selectedGRs.map(gr => (
                  <Tag
                    key={gr}
                    closable
                    onClose={() => handleRemoveGR(gr)}
                    color="blue"
                  >
                    {gr}
                  </Tag>
                ))}
              </Space>
            ) : (
              <Text type="secondary" className="text-sm">
                暂无选择的GR类型
              </Text>
            )}
          </div>
        </div>

        <Divider className="my-4" />

        {/* 刷新间隔设置 */}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="刷新间隔 (秒)"
              name="refreshInterval"
              rules={[
                { required: true, message: '请输入刷新间隔' },
                { type: 'number', min: 1, max: 60, message: '刷新间隔应在1-60秒之间' }
              ]}
            >
              <InputNumber
                min={1}
                max={60}
                className="w-full text-center"
                placeholder="输入刷新间隔"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label=" " className="mb-0">
              <Button 
                type="primary"
                icon={<CheckOutlined />}
                onClick={handleConfirmInterval}
                className="w-full"
              >
                确定
              </Button>
            </Form.Item>
          </Col>
        </Row>

        {/* 状态信息 */}
        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <Space direction="vertical" size="small" className="w-full">
            <div className="flex justify-between">
              <Text className="text-slate-600 dark:text-white">当前GR数量:</Text>
              <Text strong className="text-slate-800 dark:text-white">{selectedGRs.length}</Text>
            </div>
            <div className="flex justify-between">
              <Text className="text-slate-600 dark:text-white">曲线平滑:</Text>
              <Text strong className="text-slate-800 dark:text-white">{smoothEnabled ? '已启用' : '已禁用'}</Text>
            </div>
            <div className="flex justify-between">
              <Text className="text-slate-600 dark:text-white">刷新间隔:</Text>
              <Text strong className="text-slate-800 dark:text-white">{form.getFieldValue('refreshInterval') || 5} 秒</Text>
            </div>
          </Space>
        </div>
      </Form>
    </ModernCard>
  );
};
