// 仅测试SQLite连接
// 使用方法: cargo run --example test_sqlite_only

use sqlx::{SqlitePool, Row};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🗄️ SQLite连接测试");
    println!("================");
    
    // 测试不同的SQLite URL格式
    let test_urls = vec![
        "sqlite:data/test.db",
        "sqlite://data/test.db", 
        "sqlite:./data/test.db",
        "sqlite://./data/test.db",
        "sqlite:test.db",
    ];
    
    for (i, url) in test_urls.iter().enumerate() {
        println!("\n{}. 测试URL: {}", i + 1, url);
        
        match SqlitePool::connect(url).await {
            Ok(pool) => {
                println!("   ✅ 连接成功！");
                
                // 测试简单查询
                match sqlx::query("SELECT 1 as test").fetch_one(&pool).await {
                    Ok(row) => {
                        let value: i32 = row.get("test");
                        println!("   ✅ 查询成功，结果: {}", value);
                    }
                    Err(e) => {
                        println!("   ❌ 查询失败: {}", e);
                    }
                }
                
                pool.close().await;
                break; // 找到一个可用的就停止
            }
            Err(e) => {
                println!("   ❌ 连接失败: {}", e);
            }
        }
    }
    
    println!("\n🎉 SQLite测试完成！");
    Ok(())
}
