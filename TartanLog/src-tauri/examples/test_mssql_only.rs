// 仅测试MSSQL连接
// 使用方法: cargo run --example test_mssql_only --features mssql

use std::sync::Arc;
use tokio::sync::RwLock;

// 引入项目模块
use tartanlog_lib::config::AppConfig;
use tartanlog_lib::database::DatabaseManager;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    println!("🚀 TartanLog MSSQL连接测试 (仅MSSQL)");
    println!("==================================");
    
    // 1. 初始化配置
    println!("📋 加载配置...");
    AppConfig::init()?;
    let config = AppConfig::global();
    
    println!("✅ 配置加载成功");
    println!("   环境: {}", std::env::var("TARTANLOG_ENV").unwrap_or_else(|_| "development".to_string()));
    println!("   MSSQL URL: {}", config.database().primary_url);
    
    // 2. 创建数据库管理器
    println!("\n🔧 创建数据库管理器...");
    let db_manager = Arc::new(RwLock::new(DatabaseManager::new()));
    
    // 3. 测试MSSQL连接
    #[cfg(feature = "mssql")]
    {
        println!("\n🗄️  测试MSSQL连接...");
        let mut manager = db_manager.write().await;
        match manager.connect_mssql(&config.database().primary_url).await {
            Ok(_) => {
                println!("✅ MSSQL连接成功！");
                
                // 4. 测试简单查询
                println!("\n🔍 测试MSSQL查询...");
                if let Some(client) = manager.get_mssql_client() {
                    let mut client_guard = client.write().await;
                    let stream = client_guard.simple_query("SELECT @@VERSION as version").await;
                    match stream {
                        Ok(stream) => {
                            if let Some(row) = stream.into_row().await? {
                                let version: &str = row.get("version").unwrap_or("Unknown");
                                println!("✅ 查询成功");
                                println!("   SQL Server版本: {}", version);
                            }
                        }
                        Err(e) => {
                            println!("❌ 查询失败: {}", e);
                        }
                    }
                }
                
                // 5. 测试数据库查询
                println!("\n📊 测试数据库表查询...");
                if let Some(client) = manager.get_mssql_client() {
                    let mut client_guard = client.write().await;
                    let stream = client_guard.simple_query("SELECT COUNT(*) as table_count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'").await;
                    match stream {
                        Ok(stream) => {
                            if let Some(row) = stream.into_row().await? {
                                let count: i32 = row.get("table_count").unwrap_or(0);
                                println!("✅ 数据库表查询成功");
                                println!("   数据库中共有 {} 个表", count);
                            }
                        }
                        Err(e) => {
                            println!("❌ 数据库表查询失败: {}", e);
                        }
                    }
                }
                
                // 6. 测试特定表查询 (如果存在)
                println!("\n🎯 测试Gamma相关表查询...");
                if let Some(client) = manager.get_mssql_client() {
                    let mut client_guard = client.write().await;
                    let stream = client_guard.simple_query("SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME LIKE '%Results%' OR TABLE_NAME LIKE '%Gamma%'").await;
                    match stream {
                        Ok(stream) => {
                            if let Some(row) = stream.into_row().await? {
                                let count: i32 = row.get("count").unwrap_or(0);
                                println!("✅ Gamma相关表查询成功");
                                println!("   找到 {} 个相关表", count);
                            }
                        }
                        Err(e) => {
                            println!("❌ Gamma相关表查询失败: {}", e);
                        }
                    }
                }
            }
            Err(e) => {
                println!("❌ MSSQL连接失败: {}", e);
                println!("\n🔧 故障排除建议:");
                println!("   - 检查服务器地址: 192.168.31.12");
                println!("   - 检查端口: 1433");
                println!("   - 验证用户名: sa");
                println!("   - 验证密码: Clash1234!!");
                println!("   - 确认SQL Server允许远程连接");
                println!("   - 检查防火墙设置");
                println!("   - 确认数据库名称: MEZINTELMWD");
                
                return Err(e.into());
            }
        }
    }
    
    #[cfg(not(feature = "mssql"))]
    {
        println!("\n⚠️  MSSQL功能未启用");
        println!("   使用 --features mssql 启用MSSQL支持");
    }
    
    println!("\n🎉 MSSQL测试完成！");
    Ok(())
}
