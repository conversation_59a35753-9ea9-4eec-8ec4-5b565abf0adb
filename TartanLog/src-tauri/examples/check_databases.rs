// 检查SQL Server中的数据库
// 使用方法: cargo run --example check_databases --features mssql

use std::sync::Arc;
use tokio::sync::RwLock;

// 引入项目模块
use tartanlog_lib::config::AppConfig;
use tartanlog_lib::database::DatabaseManager;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    println!("🔍 检查SQL Server数据库");
    println!("====================");
    
    // 1. 初始化配置
    AppConfig::init()?;
    let config = AppConfig::global();
    
    println!("连接到: {}", config.database().primary_url);
    
    // 2. 创建数据库管理器并连接
    let db_manager = Arc::new(RwLock::new(DatabaseManager::new()));
    let mut manager = db_manager.write().await;
    manager.connect_mssql(&config.database().primary_url).await?;
    
    println!("✅ 连接成功");
    
    // 3. 查询所有数据库
    println!("\n📋 查询所有数据库:");
    if let Some(client) = manager.get_mssql_client() {
        let mut client_guard = client.write().await;
        let stream = client_guard.simple_query("SELECT name FROM sys.databases ORDER BY name").await?;

        // 简单地获取第一行来测试
        if let Some(row) = stream.into_row().await? {
            let db_name: &str = row.get("name").unwrap_or("Unknown");
            println!("   第一个数据库: {}", db_name);
        }

        // 重新查询来检查MEZINTELMWD
        let stream = client_guard.simple_query("SELECT name FROM sys.databases WHERE name = 'MEZINTELMWD'").await?;
        if let Some(row) = stream.into_row().await? {
            let db_name: &str = row.get("name").unwrap_or("Unknown");
            println!("   🎯 找到目标数据库: {}", db_name);
        } else {
            println!("   ❌ 未找到MEZINTELMWD数据库");
        }
        
        // 4. 特别检查MEZINTELMWD数据库
        println!("\n🎯 检查MEZINTELMWD数据库:");
        let stream = client_guard.simple_query("SELECT COUNT(*) as count FROM sys.databases WHERE name = 'MEZINTELMWD'").await?;
        if let Some(row) = stream.into_row().await? {
            let count: i32 = row.get("count").unwrap_or(0);
            if count > 0 {
                println!("✅ MEZINTELMWD数据库存在");
                
                // 5. 如果存在，尝试连接到MEZINTELMWD数据库
                println!("\n🔄 尝试连接到MEZINTELMWD数据库...");
                drop(client_guard);
                drop(manager);
                
                // 更新配置连接到MEZINTELMWD
                let mezintel_url = config.database().primary_url.replace("/master", "/MEZINTELMWD");
                println!("新连接URL: {}", mezintel_url);
                
                let mut manager = db_manager.write().await;
                match manager.connect_mssql(&mezintel_url).await {
                    Ok(_) => {
                        println!("✅ 成功连接到MEZINTELMWD数据库");
                        
                        // 6. 查询MEZINTELMWD数据库中的表数量
                        println!("\n📊 查询MEZINTELMWD数据库中的表:");
                        if let Some(client) = manager.get_mssql_client() {
                            let mut client_guard = client.write().await;
                            let stream = client_guard.simple_query("SELECT COUNT(*) as table_count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'").await?;

                            if let Some(row) = stream.into_row().await? {
                                let count: i32 = row.get("table_count").unwrap_or(0);
                                println!("   找到 {} 个表", count);
                            }

                            // 查询重要的表
                            let stream = client_guard.simple_query("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND (TABLE_NAME LIKE '%Results%' OR TABLE_NAME LIKE '%LOG_%' OR TABLE_NAME LIKE '%Survey%' OR TABLE_NAME LIKE '%Gamma%') ORDER BY TABLE_NAME").await?;

                            println!("   重要的表:");
                            if let Some(row) = stream.into_row().await? {
                                let table_name: &str = row.get("TABLE_NAME").unwrap_or("Unknown");
                                println!("   - {} 🎯", table_name);
                            } else {
                                println!("   - 未找到相关表");
                            }
                        }
                    }
                    Err(e) => {
                        println!("❌ 连接MEZINTELMWD数据库失败: {}", e);
                    }
                }
            } else {
                println!("❌ MEZINTELMWD数据库不存在");
                println!("   建议检查数据库名称或创建数据库");
            }
        }
    }
    
    println!("\n🎉 数据库检查完成！");
    Ok(())
}
