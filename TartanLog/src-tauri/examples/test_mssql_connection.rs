// MSSQL连接测试示例
// 使用方法: cargo run --example test_mssql_connection --features mssql

use std::sync::Arc;
use tokio::sync::RwLock;

// 引入项目模块
use tartanlog_lib::config::AppConfig;
use tartanlog_lib::database::DatabaseManager;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    println!("🚀 TartanLog MSSQL连接测试");
    println!("========================");
    
    // 1. 初始化配置
    println!("📋 加载配置...");
    AppConfig::init()?;
    let config = AppConfig::global();
    
    println!("✅ 配置加载成功");
    println!("   环境: {}", std::env::var("TARTANLOG_ENV").unwrap_or_else(|_| "development".to_string()));
    println!("   MSSQL URL: {}", config.database().primary_url);
    println!("   SQLite URL: {}", config.database().local_url);
    
    // 2. 创建数据库管理器
    println!("\n🔧 创建数据库管理器...");
    let db_manager = Arc::new(RwLock::new(DatabaseManager::new()));
    
    // 3. 测试SQLite连接
    println!("\n📦 测试SQLite连接...");
    {
        let mut manager = db_manager.write().await;
        match manager.connect_sqlite(&config.database().local_url).await {
            Ok(_) => println!("✅ SQLite连接成功"),
            Err(e) => {
                println!("❌ SQLite连接失败: {}", e);
                return Err(e.into());
            }
        }
    }
    
    // 4. 测试MSSQL连接
    #[cfg(feature = "mssql")]
    {
        println!("\n🗄️  测试MSSQL连接...");
        let mut manager = db_manager.write().await;
        match manager.connect_mssql(&config.database().primary_url).await {
            Ok(_) => {
                println!("✅ MSSQL连接成功");
                
                // 5. 执行健康检查
                println!("\n🏥 执行数据库健康检查...");
                match manager.health_check().await {
                    Ok(health) => {
                        println!("✅ 健康检查完成");
                        println!("   MSSQL状态: {}", health.mssql_status);
                        println!("   SQLite状态: {}", health.sqlite_status);
                    }
                    Err(e) => {
                        println!("⚠️  健康检查失败: {}", e);
                    }
                }
                
                // 6. 测试简单查询
                println!("\n🔍 测试MSSQL查询...");
                if let Some(client) = manager.get_mssql_client() {
                    let mut client_guard = client.write().await;
                    let stream = client_guard.simple_query("SELECT @@VERSION as version").await;
                    match stream {
                        Ok(stream) => {
                            if let Some(row) = stream.into_row().await? {
                                let version: &str = row.get("version").unwrap_or("Unknown");
                                println!("✅ 查询成功");
                                println!("   SQL Server版本: {}", version);
                            }
                        }
                        Err(e) => {
                            println!("❌ 查询失败: {}", e);
                        }
                    }
                }
            }
            Err(e) => {
                println!("❌ MSSQL连接失败: {}", e);
                println!("\n🔧 故障排除建议:");
                
                if config.database().primary_url.contains("trusted_connection=true") {
                    println!("   - 确保在Windows系统上运行");
                    println!("   - 检查当前用户是否有数据库访问权限");
                    println!("   - 确认SQL Server启用了Windows身份验证");
                } else {
                    println!("   - 检查服务器地址和端口是否正确");
                    println!("   - 验证用户名和密码");
                    println!("   - 确认SQL Server允许远程连接");
                }
                println!("   - 检查防火墙设置");
                println!("   - 确认数据库名称正确");
                
                return Err(e.into());
            }
        }
    }
    
    #[cfg(not(feature = "mssql"))]
    {
        println!("\n⚠️  MSSQL功能未启用");
        println!("   使用 --features mssql 启用MSSQL支持");
    }
    
    println!("\n🎉 测试完成！");
    Ok(())
}
