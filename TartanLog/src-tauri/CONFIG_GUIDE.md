# TartanLog 配置指南

## 📁 配置文件结构

```
src-tauri/
├── config/
│   ├── app.toml           # 基础配置
│   ├── development.toml   # 开发环境配置
│   └── production.toml    # 生产环境配置
├── .env.example          # 环境变量示例
└── src/config/           # Rust配置代码
    ├── mod.rs
    └── validation.rs
```

## 🔧 配置加载顺序

1. **基础配置** (`config/app.toml`) - 默认配置
2. **环境配置** (`config/{env}.toml`) - 覆盖基础配置
3. **环境变量** (`TARTANLOG_*`) - 最高优先级

## 🌍 环境设置

通过环境变量 `TARTANLOG_ENV` 设置：

```bash
# 开发环境
export TARTANLOG_ENV=development

# 生产环境
export TARTANLOG_ENV=production
```

## 📝 配置项说明

### 数据库配置 (`[database]`)

- `primary_url`: 主数据库连接字符串 (MySQL)
- `local_url`: 本地SQLite数据库路径
- `max_connections`: 最大连接数
- `min_connections`: 最小连接数
- `connection_timeout`: 连接超时时间(秒)

### Gamma配置 (`[gamma]`)

- `default_refresh_interval`: 默认刷新间隔(秒)
- `supported_sectors`: 支持的扇区数列表
- `default_sector_count`: 默认扇区数
- `smooth_curve_default`: 是否默认启用曲线平滑

### 性能配置 (`[performance]`)

- `enable_mimalloc`: 是否启用内存优化
- `data_cache_size`: 数据缓存大小
- `worker_threads`: 工作线程数

## 🔒 环境变量覆盖

使用双下划线 `__` 分隔嵌套配置：

```bash
# 覆盖 database.primary_url
TARTANLOG_DATABASE__PRIMARY_URL=mysql://user:pass@host:3306/db

# 覆盖 gamma.default_refresh_interval
TARTANLOG_GAMMA__DEFAULT_REFRESH_INTERVAL=30
```

## 🚀 在代码中使用配置

```rust
use crate::config::AppConfig;

// 获取全局配置
let config = AppConfig::global();

// 访问数据库配置
let db_url = &config.database().primary_url;

// 访问Gamma配置
let refresh_interval = config.gamma().default_refresh_interval;

// 检查功能是否启用
if config.is_data_transmission_enabled() {
    // 数据传输逻辑
}
```

## ✅ 配置验证

应用启动时会自动验证配置：

- 数据库连接参数合理性
- 端口号范围检查
- 文件路径有效性
- 性能参数合理性

## 🔍 调试配置

启用调试日志查看配置加载过程：

```bash
TARTANLOG_LOGGING__LEVEL=debug cargo run
```

## 📋 配置检查清单

开发环境：
- [ ] 复制 `.env.example` 为 `.env`
- [ ] 设置正确的数据库连接
- [ ] 确认日志目录可写
- [ ] 验证端口未被占用

生产环境：
- [ ] 使用安全的数据库密码
- [ ] 设置适当的日志级别
- [ ] 配置数据传输参数
- [ ] 优化性能参数
