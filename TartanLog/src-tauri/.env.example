# TartanLog 环境变量示例文件
# 复制此文件为 .env 并根据你的环境修改配置

# 应用环境 (development, production)
TARTANLOG_ENV=development

# 数据库配置
TARTANLOG_DATABASE__PRIMARY_URL=mysql://root:password@localhost:3306/MEZINTELMWD
TARTANLOG_DATABASE__LOCAL_URL=sqlite:./data/tartanlog.db
TARTANLOG_DATABASE__MAX_CONNECTIONS=10

# 服务器配置
TARTANLOG_SERVER__HOST=127.0.0.1
TARTANLOG_SERVER__PORT=8080

# Gamma配置
TARTANLOG_GAMMA__DEFAULT_REFRESH_INTERVAL=60
TARTANLOG_GAMMA__DEFAULT_SECTOR_COUNT=4

# 数据传输配置
TARTANLOG_DATA_TRANSMISSION__ENABLED=false
TARTANLOG_DATA_TRANSMISSION__IP=*************
TARTANLOG_DATA_TRANSMISSION__PORT=9001

# 日志配置
TARTANLOG_LOGGING__LEVEL=info
TARTANLOG_LOGGING__FILE_PATH=./logs/tartanlog.log

# 性能配置
TARTANLOG_PERFORMANCE__ENABLE_MIMALLOC=true
TARTANLOG_PERFORMANCE__WORKER_THREADS=4
