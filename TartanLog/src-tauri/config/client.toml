# 客户环境配置
# 使用Windows集成身份验证连接到客户的SQL Server

[database]
# 客户环境使用Windows集成身份验证连接到本地SQLEXPRESS
# 对应C#中的: "Data Source=.\\SQLEXPRESS;Initial Catalog =MEZINTELMWD;Integrated Security=true;"
primary_url = "mssql://localhost\\SQLEXPRESS/MEZINTELMWD?trusted_connection=true"
local_url = "sqlite:./data/client.db"
max_connections = 10
min_connections = 2

[logging]
level = "info"
file_path = "./logs/client.log"

[server]
port = 8080

[gamma]
default_refresh_interval = 60

[performance]
enable_mimalloc = true
worker_threads = 4
