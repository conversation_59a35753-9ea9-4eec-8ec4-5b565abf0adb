# TartanLog 应用配置文件
# 这个文件包含应用的主要配置项

[app]
name = "TartanLog"
version = "1.0.0"
description = "达坦实时Gamma成像系统"

# 服务器配置
[server]
host = "127.0.0.1"
port = 8080
timeout_seconds = 30

# 数据库配置
[database]
# 主数据库 (MySQL替代原来的MSSQL)
primary_url = "mysql://root:password@localhost:3306/MEZINTELMWD"
# 本地SQLite数据库用于缓存和配置
local_url = "sqlite:./data/tartanlog.db"
# 连接池配置
max_connections = 10
min_connections = 2
connection_timeout = 30

# Gamma成像系统配置
[gamma]
# 默认刷新间隔（秒）
default_refresh_interval = 60
# 支持的扇区数
supported_sectors = [2, 4]
# 默认扇区数
default_sector_count = 4
# 曲线平滑默认开启
smooth_curve_default = true

# 数据传输配置
[data_transmission]
enabled = false
ip = "*************"
port = 9001
protocol = "TCP"  # TCP 或 UDP

# 日志配置
[logging]
level = "info"  # trace, debug, info, warn, error
file_path = "./logs/tartanlog.log"
max_file_size = "10MB"
max_files = 5

# 性能配置
[performance]
# 是否启用内存优化
enable_mimalloc = true
# 实时数据缓存大小
data_cache_size = 1000
# 并发处理线程数
worker_threads = 4
