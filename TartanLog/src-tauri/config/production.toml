# 生产环境配置
# 这个文件会覆盖 app.toml 中的配置项

[database]
# 生产环境数据库配置
primary_url = "mysql://tartanlog_user:secure_password@production-server:3306/MEZINTELMWD"
local_url = "sqlite:./data/production.db"
max_connections = 20
min_connections = 5

[logging]
level = "warn"  # 生产环境只记录警告和错误
file_path = "/var/log/tartanlog/app.log"

[server]
host = "0.0.0.0"  # 监听所有接口
port = 8080

[gamma]
default_refresh_interval = 60

[performance]
enable_mimalloc = true
worker_threads = 8  # 生产环境更多线程

[data_transmission]
enabled = true
ip = "*************"
port = 9001
