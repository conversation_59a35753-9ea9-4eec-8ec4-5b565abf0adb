# 开发环境配置
# 这个文件会覆盖 app.toml 中的配置项

[database]
# 开发环境使用本地MSSQL数据库 (与C#配置一致)
primary_url = "mssql://localhost\\SQLEXPRESS/MEZINTELMWD?trusted_connection=true"
local_url = "sqlite:./data/dev.db"
max_connections = 5

[logging]
level = "debug"
file_path = "./logs/dev.log"

[server]
port = 8081

[gamma]
default_refresh_interval = 10  # 开发时更频繁的刷新

[performance]
# 开发时关闭一些优化以便调试
enable_mimalloc = false
worker_threads = 2
