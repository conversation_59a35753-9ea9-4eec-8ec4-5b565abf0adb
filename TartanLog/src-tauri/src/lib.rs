// TartanLog - 达坦实时Gamma成像系统
// 主应用模块

// 模块声明
pub mod config;
pub mod database;

// 导入必要的依赖
use config::AppConfig;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use std::sync::Arc;

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

/// 获取应用配置信息
#[tauri::command]
fn get_app_info() -> Result<config::AppInfo, String> {
    let config = AppConfig::global();
    Ok(config.app.clone())
}

/// 获取Gamma配置
#[tauri::command]
fn get_gamma_config() -> Result<config::GammaConfig, String> {
    let config = AppConfig::global();
    Ok(config.gamma().clone())
}

/// 获取数据库健康状态
#[tauri::command]
async fn get_database_health(
    db_manager: tauri::State<'_, Arc<tokio::sync::RwLock<database::DatabaseManager>>>,
) -> Result<database::DatabaseHealth, String> {
    let manager = db_manager.read().await;
    manager.health_check().await.map_err(|e| e.to_string())
}

/// 测试数据库连接
#[tauri::command]
async fn test_database_connection(
    db_manager: tauri::State<'_, Arc<tokio::sync::RwLock<database::DatabaseManager>>>,
) -> Result<String, String> {
    let config = AppConfig::global();
    let mut manager = db_manager.write().await;

    // 连接SQLite
    manager.connect_sqlite(&config.database().local_url)
        .await
        .map_err(|e| format!("SQLite connection failed: {}", e))?;

    // 连接MSSQL（如果启用了feature）
    #[cfg(feature = "mssql")]
    {
        manager.connect_mssql(&config.database().primary_url)
            .await
            .map_err(|e| format!("MSSQL connection failed: {}", e))?;
    }

    Ok("Database connections established successfully".to_string())
}

/// 初始化日志系统
fn init_logging() -> eyre::Result<()> {
    let config = AppConfig::global();

    // 根据配置设置日志级别
    let level = match config.log_level() {
        "trace" => tracing::Level::TRACE,
        "debug" => tracing::Level::DEBUG,
        "info" => tracing::Level::INFO,
        "warn" => tracing::Level::WARN,
        "error" => tracing::Level::ERROR,
        _ => tracing::Level::INFO,
    };

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| format!("tartanlog={}", level).into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 1. 初始化配置
    if let Err(e) = AppConfig::init() {
        eprintln!("Failed to initialize configuration: {}", e);
        std::process::exit(1);
    }

    // 2. 初始化日志
    if let Err(e) = init_logging() {
        eprintln!("Failed to initialize logging: {}", e);
        std::process::exit(1);
    }

    // 3. 运行配置健康检查
    if let Err(e) = config::validation::health_check() {
        tracing::error!("Configuration health check failed: {}", e);
        std::process::exit(1);
    }

    tracing::info!("Starting TartanLog application...");

    // 4. 初始化数据库管理器
    let db_manager = Arc::new(tokio::sync::RwLock::new(database::DatabaseManager::new()));

    // 5. 启动Tauri应用
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(db_manager)
        .invoke_handler(tauri::generate_handler![
            greet,
            get_app_info,
            get_gamma_config,
            get_database_health,
            test_database_connection
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
