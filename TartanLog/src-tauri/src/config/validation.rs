// 配置验证模块
// 确保配置的有效性和合理性

use super::*;
use eyre::{eyre, Result};

impl AppConfig {
    /// 验证配置的有效性
    pub fn validate(&self) -> Result<()> {
        self.validate_database()?;
        self.validate_gamma()?;
        self.validate_server()?;
        self.validate_performance()?;
        Ok(())
    }

    /// 验证数据库配置
    fn validate_database(&self) -> Result<()> {
        let db = &self.database;

        // 检查连接池配置
        if db.max_connections < db.min_connections {
            return Err(eyre!(
                "Database max_connections ({}) must be >= min_connections ({})",
                db.max_connections,
                db.min_connections
            ));
        }

        if db.min_connections == 0 {
            return Err(eyre!("Database min_connections must be > 0"));
        }

        // 检查连接超时
        if db.connection_timeout == 0 {
            return Err(eyre!("Database connection_timeout must be > 0"));
        }

        // 验证URL格式（简单检查）
        if !db.primary_url.starts_with("mssql://") {
            return Err(eyre!("Primary database URL must start with 'mssql://'"));
        }

        Ok(())
    }

    /// 验证Gamma配置
    fn validate_gamma(&self) -> Result<()> {
        let gamma = &self.gamma;

        // 检查扇区数配置
        if !gamma.supported_sectors.contains(&gamma.default_sector_count) {
            return Err(eyre!(
                "Default sector count ({}) must be in supported sectors ({:?})",
                gamma.default_sector_count,
                gamma.supported_sectors
            ));
        }

        // 检查刷新间隔
        if gamma.default_refresh_interval == 0 {
            return Err(eyre!("Gamma refresh interval must be > 0"));
        }

        if gamma.default_refresh_interval > 3600 {
            tracing::warn!(
                "Gamma refresh interval ({}) is very long (> 1 hour)",
                gamma.default_refresh_interval
            );
        }

        Ok(())
    }

    /// 验证服务器配置
    fn validate_server(&self) -> Result<()> {
        let server = &self.server;

        // 检查端口范围
        if server.port < 1024 {
            tracing::warn!("Server port {} is in privileged range (< 1024)", server.port);
        }

        // 检查超时配置
        if server.timeout_seconds == 0 {
            return Err(eyre!("Server timeout must be > 0"));
        }

        Ok(())
    }

    /// 验证性能配置
    fn validate_performance(&self) -> Result<()> {
        let perf = &self.performance;

        // 检查缓存大小
        if perf.data_cache_size == 0 {
            return Err(eyre!("Data cache size must be > 0"));
        }

        // 检查线程数
        if perf.worker_threads == 0 {
            return Err(eyre!("Worker threads must be > 0"));
        }

        let cpu_count = std::thread::available_parallelism()
            .map(|n| n.get())
            .unwrap_or(1);

        if perf.worker_threads > cpu_count * 2 {
            tracing::warn!(
                "Worker threads ({}) is much higher than CPU count ({})",
                perf.worker_threads,
                cpu_count
            );
        }

        Ok(())
    }
}

/// 配置健康检查
pub fn health_check() -> Result<()> {
    let config = AppConfig::global();
    
    tracing::info!("Running configuration health check...");
    
    // 验证配置
    config.validate()?;
    
    // 检查文件路径
    check_file_paths(config)?;
    
    // 检查网络连接（可选）
    check_network_connectivity(config)?;
    
    tracing::info!("Configuration health check passed");
    Ok(())
}

/// 检查文件路径的有效性
fn check_file_paths(config: &AppConfig) -> Result<()> {
    use std::path::Path;

    // 检查日志目录
    let log_path = Path::new(&config.logging.file_path);
    if let Some(parent) = log_path.parent() {
        if !parent.exists() {
            std::fs::create_dir_all(parent)?;
            tracing::info!("Created log directory: {}", parent.display());
        }
    }

    // 检查数据目录
    // SQLite相关的目录创建已移除，因为不再使用SQLite

    Ok(())
}

/// 检查网络连接性（简单检查）
fn check_network_connectivity(_config: &AppConfig) -> Result<()> {
    // 这里可以添加数据库连接测试
    // 目前只是占位符
    tracing::debug!("Network connectivity check skipped");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_database_validation() {
        let mut config = create_test_config();
        
        // 测试正常情况
        assert!(config.validate_database().is_ok());
        
        // 测试错误情况：max < min
        config.database.max_connections = 1;
        config.database.min_connections = 5;
        assert!(config.validate_database().is_err());
    }

    fn create_test_config() -> AppConfig {
        AppConfig {
            app: AppInfo {
                name: "Test".to_string(),
                version: "1.0.0".to_string(),
                description: "Test".to_string(),
            },
            server: ServerConfig {
                host: "127.0.0.1".to_string(),
                port: 8080,
                timeout_seconds: 30,
            },
            database: DatabaseConfig {
                primary_url: "mssql://test".to_string(),
                local_url: "sqlite:test.db".to_string(),
                max_connections: 10,
                min_connections: 2,
                connection_timeout: 30,
            },
            gamma: GammaConfig {
                default_refresh_interval: 60,
                supported_sectors: vec![2, 4],
                default_sector_count: 4,
                smooth_curve_default: true,
            },
            data_transmission: DataTransmissionConfig {
                enabled: false,
                ip: "127.0.0.1".to_string(),
                port: 9001,
                protocol: "TCP".to_string(),
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                file_path: "./test.log".to_string(),
                max_file_size: "10MB".to_string(),
                max_files: 5,
            },
            performance: PerformanceConfig {
                enable_mimalloc: false,
                data_cache_size: 1000,
                worker_threads: 4,
            },
        }
    }
}
