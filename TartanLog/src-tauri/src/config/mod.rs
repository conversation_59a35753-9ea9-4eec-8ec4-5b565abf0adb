// 配置管理模块
// 这个模块负责加载和管理应用配置

pub mod validation;

use figment::{Figment, providers::{Format, Toml, Env}};
use serde::{Deserialize, Serialize};
use std::sync::OnceLock;

// 全局配置实例
static CONFIG: OnceLock<AppConfig> = OnceLock::new();

/// 应用主配置结构
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AppConfig {
    pub app: AppInfo,
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub gamma: GammaConfig,
    pub data_transmission: DataTransmissionConfig,
    pub logging: LoggingConfig,
    pub performance: PerformanceConfig,
}

/// 应用信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppInfo {
    pub name: String,
    pub version: String,
    pub description: String,
}

/// 服务器配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
    pub timeout_seconds: u64,
}

/// 数据库配置（仅MSSQL）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub primary_url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connection_timeout: u64,
}

/// Gamma成像配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GammaConfig {
    pub default_refresh_interval: u64,
    pub supported_sectors: Vec<u8>,
    pub default_sector_count: u8,
    pub smooth_curve_default: bool,
}

/// 数据传输配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataTransmissionConfig {
    pub enabled: bool,
    pub ip: String,
    pub port: u16,
    pub protocol: String,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub file_path: String,
    pub max_file_size: String,
    pub max_files: u32,
}

/// 性能配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    pub enable_mimalloc: bool,
    pub data_cache_size: usize,
    pub worker_threads: usize,
}

impl AppConfig {
    /// 加载配置
    /// 
    /// 加载顺序：
    /// 1. 基础配置 (app.toml)
    /// 2. 环境特定配置 (development.toml 或 production.toml)
    /// 3. 环境变量 (TARTANLOG_ 前缀)
    pub fn load() -> eyre::Result<Self> {
        // 确定环境
        let env = std::env::var("TARTANLOG_ENV").unwrap_or_else(|_| "development".to_string());
        
        tracing::info!("Loading configuration for environment: {}", env);

        let config = Figment::new()
            // 1. 加载基础配置
            .merge(Toml::file("config/app.toml"))
            // 2. 加载环境特定配置
            .merge(Toml::file(format!("config/{}.toml", env)))
            // 3. 加载环境变量 (TARTANLOG_DATABASE__PRIMARY_URL 等)
            .merge(Env::prefixed("TARTANLOG_").split("__"))
            .extract()?;

        tracing::info!("Configuration loaded successfully");
        Ok(config)
    }

    /// 获取全局配置实例
    pub fn global() -> &'static AppConfig {
        CONFIG.get().expect("Configuration not initialized. Call AppConfig::init() first.")
    }

    /// 初始化全局配置
    pub fn init() -> eyre::Result<()> {
        let config = Self::load()?;
        CONFIG.set(config).map_err(|_| eyre::eyre!("Configuration already initialized"))?;
        Ok(())
    }
}

// 便捷访问函数
impl AppConfig {
    /// 获取数据库配置
    pub fn database(&self) -> &DatabaseConfig {
        &self.database
    }

    /// 获取Gamma配置
    pub fn gamma(&self) -> &GammaConfig {
        &self.gamma
    }

    /// 获取服务器配置
    pub fn server(&self) -> &ServerConfig {
        &self.server
    }

    /// 是否启用数据传输
    pub fn is_data_transmission_enabled(&self) -> bool {
        self.data_transmission.enabled
    }

    /// 获取日志级别
    pub fn log_level(&self) -> &str {
        &self.logging.level
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_config_loading() {
        // 这个测试需要配置文件存在
        // 在实际项目中，你可以创建测试专用的配置文件
        std::env::set_var("TARTANLOG_ENV", "development");
        
        let result = AppConfig::load();
        match result {
            Ok(config) => {
                assert_eq!(config.app.name, "TartanLog");
                assert!(config.gamma.supported_sectors.contains(&2));
                assert!(config.gamma.supported_sectors.contains(&4));
            }
            Err(e) => {
                // 如果配置文件不存在，测试会失败，这是正常的
                println!("Config loading failed (expected in test environment): {}", e);
            }
        }
    }
}
