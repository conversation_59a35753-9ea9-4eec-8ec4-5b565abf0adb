// 数据库管理模块
// 支持MSSQL (Tiberius) + SQLite (SQLx)

use std::sync::Arc;
use tokio::sync::RwLock;
use eyre::Result;

#[cfg(feature = "mssql")]
use tiberius::{Client, Config, AuthMethod};
#[cfg(feature = "mssql")]
use tokio::net::TcpStream;
#[cfg(feature = "mssql")]
use tokio_util::compat::{TokioAsyncWriteCompatExt, Compat};

use sqlx::SqlitePool;

/// 数据库管理器
/// 
/// 支持两种数据库：
/// - MSSQL: 使用Tiberius客户端连接主数据库
/// - SQLite: 使用SQLx连接本地缓存数据库
pub struct DatabaseManager {
    #[cfg(feature = "mssql")]
    pub mssql_client: Option<Arc<RwLock<Client<Compat<TcpStream>>>>>,
    pub sqlite_pool: Option<Arc<SqlitePool>>,
}

impl DatabaseManager {
    /// 创建新的数据库管理器
    pub fn new() -> Self {
        Self {
            #[cfg(feature = "mssql")]
            mssql_client: None,
            sqlite_pool: None,
        }
    }

    /// 连接到MSSQL数据库
    #[cfg(feature = "mssql")]
    pub async fn connect_mssql(&mut self, connection_url: &str) -> Result<()> {
        tracing::info!("Connecting to MSSQL database...");
        
        // 解析连接URL: mssql://username:password@host:port/database
        let config = parse_mssql_url(connection_url)?;
        
        // 建立TCP连接
        let tcp = TcpStream::connect(config.get_addr()).await?;
        tcp.set_nodelay(true)?;
        
        // 创建Tiberius客户端
        let mut client = Client::connect(config, tcp.compat_write()).await?;
        
        // 测试连接
        let row = client.simple_query("SELECT @@VERSION").await?;
        if let Some(row) = row.into_row().await? {
            let version: &str = row.get(0).unwrap();
            tracing::info!("Connected to SQL Server: {}", version);
        }
        
        self.mssql_client = Some(Arc::new(RwLock::new(client)));
        tracing::info!("MSSQL connection established successfully");
        
        Ok(())
    }

    /// 连接到SQLite数据库
    pub async fn connect_sqlite(&mut self, database_path: &str) -> Result<()> {
        tracing::info!("Connecting to SQLite database: {}", database_path);
        
        let pool = SqlitePool::connect(database_path).await?;
        
        // 运行迁移（如果需要）
        // TODO: 创建migrations目录后启用
        // sqlx::migrate!("./migrations").run(&pool).await?;
        
        self.sqlite_pool = Some(Arc::new(pool));
        tracing::info!("SQLite connection established successfully");
        
        Ok(())
    }

    /// 获取MSSQL客户端
    #[cfg(feature = "mssql")]
    pub fn get_mssql_client(&self) -> Option<Arc<RwLock<Client<Compat<TcpStream>>>>> {
        self.mssql_client.clone()
    }

    /// 获取SQLite连接池
    pub fn get_sqlite_pool(&self) -> Option<Arc<SqlitePool>> {
        self.sqlite_pool.clone()
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<DatabaseHealth> {
        let mut health = DatabaseHealth::default();

        // 检查SQLite连接
        if let Some(pool) = &self.sqlite_pool {
            match sqlx::query("SELECT 1").fetch_one(&**pool).await {
                Ok(_) => {
                    health.sqlite_status = "healthy".to_string();
                    tracing::debug!("SQLite health check passed");
                }
                Err(e) => {
                    health.sqlite_status = format!("error: {}", e);
                    tracing::warn!("SQLite health check failed: {}", e);
                }
            }
        } else {
            health.sqlite_status = "not connected".to_string();
        }

        // 检查MSSQL连接
        #[cfg(feature = "mssql")]
        if let Some(client) = &self.mssql_client {
            match client.write().await.simple_query("SELECT 1").await {
                Ok(_) => {
                    health.mssql_status = "healthy".to_string();
                    tracing::debug!("MSSQL health check passed");
                }
                Err(e) => {
                    health.mssql_status = format!("error: {}", e);
                    tracing::warn!("MSSQL health check failed: {}", e);
                }
            }
        } else {
            health.mssql_status = "not connected".to_string();
        }

        #[cfg(not(feature = "mssql"))]
        {
            health.mssql_status = "feature disabled".to_string();
        }

        Ok(health)
    }
}

/// 数据库健康状态
#[derive(Debug, Clone, serde::Serialize)]
pub struct DatabaseHealth {
    pub sqlite_status: String,
    pub mssql_status: String,
}

impl Default for DatabaseHealth {
    fn default() -> Self {
        Self {
            sqlite_status: "unknown".to_string(),
            mssql_status: "unknown".to_string(),
        }
    }
}

/// 解析MSSQL连接URL
#[cfg(feature = "mssql")]
fn parse_mssql_url(url: &str) -> Result<Config> {
    // 简单的URL解析: mssql://username:password@host:port/database
    let url = url.strip_prefix("mssql://")
        .ok_or_else(|| eyre::eyre!("Invalid MSSQL URL format"))?;
    
    let (auth_part, addr_part) = url.split_once('@')
        .ok_or_else(|| eyre::eyre!("Invalid MSSQL URL format: missing @"))?;
    
    let (username, password) = auth_part.split_once(':')
        .ok_or_else(|| eyre::eyre!("Invalid MSSQL URL format: missing password"))?;
    
    let (host_port, database) = addr_part.split_once('/')
        .ok_or_else(|| eyre::eyre!("Invalid MSSQL URL format: missing database"))?;
    
    let (host, port_str) = host_port.split_once(':')
        .ok_or_else(|| eyre::eyre!("Invalid MSSQL URL format: missing port"))?;
    
    let port: u16 = port_str.parse()
        .map_err(|_| eyre::eyre!("Invalid port number"))?;
    
    let mut config = Config::new();
    config.host(host);
    config.port(port);
    config.database(database);
    config.authentication(AuthMethod::sql_server(username, password));
    config.trust_cert(); // 在生产环境中应该验证证书
    
    Ok(config)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_database_manager_creation() {
        let manager = DatabaseManager::new();
        assert!(manager.sqlite_pool.is_none());
        
        #[cfg(feature = "mssql")]
        assert!(manager.mssql_client.is_none());
    }

    #[cfg(feature = "mssql")]
    #[test]
    fn test_mssql_url_parsing() {
        let url = "mssql://sa:password@localhost:1433/testdb";
        let config = parse_mssql_url(url).unwrap();
        
        // 这里可以添加更多的配置验证
        // 注意：Tiberius的Config结构体可能没有公开的getter方法
    }
}
