// 数据库管理模块
// 支持MSSQL (Tiberius) + SQLite (SQLx)

use std::sync::Arc;
use tokio::sync::RwLock;
use eyre::Result;

#[cfg(feature = "mssql")]
use tiberius::{Client, Config, AuthMethod};
#[cfg(feature = "mssql")]
use tokio::net::TcpStream;
#[cfg(feature = "mssql")]
use tokio_util::compat::{TokioAsyncWriteCompatExt, Compat};

use sqlx::SqlitePool;

/// 数据库管理器
/// 
/// 支持两种数据库：
/// - MSSQL: 使用Tiberius客户端连接主数据库
/// - SQLite: 使用SQLx连接本地缓存数据库
pub struct DatabaseManager {
    #[cfg(feature = "mssql")]
    pub mssql_client: Option<Arc<RwLock<Client<Compat<TcpStream>>>>>,
    pub sqlite_pool: Option<Arc<SqlitePool>>,
}

impl DatabaseManager {
    /// 创建新的数据库管理器
    pub fn new() -> Self {
        Self {
            #[cfg(feature = "mssql")]
            mssql_client: None,
            sqlite_pool: None,
        }
    }

    /// 连接到MSSQL数据库
    #[cfg(feature = "mssql")]
    pub async fn connect_mssql(&mut self, connection_url: &str) -> Result<()> {
        tracing::info!("Connecting to MSSQL database...");
        
        // 解析连接URL: mssql://username:password@host:port/database
        let config = parse_mssql_url(connection_url)?;
        
        // 建立TCP连接
        let tcp = TcpStream::connect(config.get_addr()).await?;
        tcp.set_nodelay(true)?;
        
        // 创建Tiberius客户端
        let mut client = Client::connect(config, tcp.compat_write()).await?;
        
        // 测试连接
        let row = client.simple_query("SELECT @@VERSION").await?;
        if let Some(row) = row.into_row().await? {
            let version: &str = row.get(0).unwrap();
            tracing::info!("Connected to SQL Server: {}", version);
        }
        
        self.mssql_client = Some(Arc::new(RwLock::new(client)));
        tracing::info!("MSSQL connection established successfully");
        
        Ok(())
    }

    /// 连接到SQLite数据库
    pub async fn connect_sqlite(&mut self, database_path: &str) -> Result<()> {
        tracing::info!("Connecting to SQLite database: {}", database_path);
        
        let pool = SqlitePool::connect(database_path).await?;
        
        // 运行迁移（如果需要）
        // TODO: 创建migrations目录后启用
        // sqlx::migrate!("./migrations").run(&pool).await?;
        
        self.sqlite_pool = Some(Arc::new(pool));
        tracing::info!("SQLite connection established successfully");
        
        Ok(())
    }

    /// 获取MSSQL客户端
    #[cfg(feature = "mssql")]
    pub fn get_mssql_client(&self) -> Option<Arc<RwLock<Client<Compat<TcpStream>>>>> {
        self.mssql_client.clone()
    }

    /// 获取SQLite连接池
    pub fn get_sqlite_pool(&self) -> Option<Arc<SqlitePool>> {
        self.sqlite_pool.clone()
    }

    /// 健康检查
    pub async fn health_check(&self) -> Result<DatabaseHealth> {
        let mut health = DatabaseHealth::default();

        // 检查SQLite连接
        if let Some(pool) = &self.sqlite_pool {
            match sqlx::query("SELECT 1").fetch_one(&**pool).await {
                Ok(_) => {
                    health.sqlite_status = "healthy".to_string();
                    tracing::debug!("SQLite health check passed");
                }
                Err(e) => {
                    health.sqlite_status = format!("error: {}", e);
                    tracing::warn!("SQLite health check failed: {}", e);
                }
            }
        } else {
            health.sqlite_status = "not connected".to_string();
        }

        // 检查MSSQL连接
        #[cfg(feature = "mssql")]
        if let Some(client) = &self.mssql_client {
            match client.write().await.simple_query("SELECT 1").await {
                Ok(_) => {
                    health.mssql_status = "healthy".to_string();
                    tracing::debug!("MSSQL health check passed");
                }
                Err(e) => {
                    health.mssql_status = format!("error: {}", e);
                    tracing::warn!("MSSQL health check failed: {}", e);
                }
            }
        } else {
            health.mssql_status = "not connected".to_string();
        }

        #[cfg(not(feature = "mssql"))]
        {
            health.mssql_status = "feature disabled".to_string();
        }

        Ok(health)
    }
}

/// 数据库健康状态
#[derive(Debug, Clone, serde::Serialize)]
pub struct DatabaseHealth {
    pub sqlite_status: String,
    pub mssql_status: String,
}

impl Default for DatabaseHealth {
    fn default() -> Self {
        Self {
            sqlite_status: "unknown".to_string(),
            mssql_status: "unknown".to_string(),
        }
    }
}

/// 解析MSSQL连接URL
/// 支持两种格式：
/// 1. SQL Server身份验证: mssql://username:password@host:port/database
/// 2. Windows集成身份验证: mssql://host:port/database?trusted_connection=true
/// 3. 命名实例: mssql://host\\instance/database?trusted_connection=true
#[cfg(feature = "mssql")]
fn parse_mssql_url(url: &str) -> Result<Config> {
    let url = url.strip_prefix("mssql://")
        .ok_or_else(|| eyre::eyre!("Invalid MSSQL URL format"))?;

    // 检查是否使用Windows集成身份验证
    if url.contains("trusted_connection=true") {
        parse_windows_auth_url(url)
    } else {
        parse_sql_auth_url(url)
    }
}

/// 解析Windows集成身份验证URL
#[cfg(feature = "mssql")]
fn parse_windows_auth_url(url: &str) -> Result<Config> {
    // 格式: host:port/database?trusted_connection=true 或 host\\instance/database?trusted_connection=true
    let (addr_db_part, _query) = url.split_once('?')
        .ok_or_else(|| eyre::eyre!("Invalid Windows auth URL format"))?;

    let (host_part, database) = addr_db_part.split_once('/')
        .ok_or_else(|| eyre::eyre!("Invalid URL format: missing database"))?;

    let mut config = Config::new();

    // 处理命名实例 (如 localhost\\SQLEXPRESS)
    if host_part.contains('\\') {
        let (host, instance) = host_part.split_once('\\')
            .ok_or_else(|| eyre::eyre!("Invalid instance format"))?;

        config.host(host);
        config.instance_name(instance);
        tracing::info!("Connecting to SQL Server instance: {}\\{}", host, instance);
    } else if host_part.contains(':') {
        // 处理端口 (如 localhost:1433)
        let (host, port_str) = host_part.split_once(':')
            .ok_or_else(|| eyre::eyre!("Invalid host:port format"))?;

        let port: u16 = port_str.parse()
            .map_err(|_| eyre::eyre!("Invalid port number"))?;

        config.host(host);
        config.port(port);
        tracing::info!("Connecting to SQL Server: {}:{}", host, port);
    } else {
        // 只有主机名，使用默认端口
        config.host(host_part);
        tracing::info!("Connecting to SQL Server: {} (default port)", host_part);
    }

    config.database(database);

    // 使用Windows集成身份验证
    #[cfg(windows)]
    {
        config.authentication(AuthMethod::windows_auth());
        tracing::info!("Using Windows integrated authentication");
    }

    #[cfg(not(windows))]
    {
        return Err(eyre::eyre!("Windows integrated authentication is only supported on Windows"));
    }

    #[cfg(windows)]
    {
        // 配置TLS设置
        config.trust_cert();
        config.encryption(tiberius::EncryptionLevel::NotSupported); // 禁用加密以避免TLS问题
        Ok(config)
    }
}

/// 解析SQL Server身份验证URL
#[cfg(feature = "mssql")]
fn parse_sql_auth_url(url: &str) -> Result<Config> {
    // 格式: username:password@host:port/database
    let (auth_part, addr_part) = url.split_once('@')
        .ok_or_else(|| eyre::eyre!("Invalid SQL auth URL format: missing @"))?;

    let (username, password) = auth_part.split_once(':')
        .ok_or_else(|| eyre::eyre!("Invalid SQL auth URL format: missing password"))?;

    let (host_port, database) = addr_part.split_once('/')
        .ok_or_else(|| eyre::eyre!("Invalid URL format: missing database"))?;

    let (host, port_str) = host_port.split_once(':')
        .ok_or_else(|| eyre::eyre!("Invalid URL format: missing port"))?;

    let port: u16 = port_str.parse()
        .map_err(|_| eyre::eyre!("Invalid port number"))?;

    let mut config = Config::new();
    config.host(host);
    config.port(port);
    config.database(database);
    config.authentication(AuthMethod::sql_server(username, password));

    // 配置TLS设置 - 对于开发环境，信任自签名证书
    config.trust_cert();
    config.encryption(tiberius::EncryptionLevel::NotSupported); // 禁用加密以避免TLS问题

    tracing::info!("Using SQL Server authentication for user: {}", username);

    Ok(config)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_database_manager_creation() {
        let manager = DatabaseManager::new();
        assert!(manager.sqlite_pool.is_none());
        
        #[cfg(feature = "mssql")]
        assert!(manager.mssql_client.is_none());
    }

    #[cfg(feature = "mssql")]
    #[test]
    fn test_sql_auth_url_parsing() {
        let url = "mssql://sa:Clash1234!!@*************:1433/MEZINTELMWD";
        let config = parse_mssql_url(url).unwrap();
        // 配置验证会在实际连接时进行
    }

    #[cfg(all(feature = "mssql", windows))]
    #[test]
    fn test_windows_auth_url_parsing() {
        // 测试命名实例
        let url = "mssql://localhost\\SQLEXPRESS/MEZINTELMWD?trusted_connection=true";
        let config = parse_mssql_url(url).unwrap();

        // 测试端口连接
        let url2 = "mssql://localhost:1433/MEZINTELMWD?trusted_connection=true";
        let config2 = parse_mssql_url(url2).unwrap();
    }
}
