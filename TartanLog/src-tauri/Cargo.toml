[package]
name = "tartanlog"
version = "1.0.0"
description = "达坦实时Gamma成像系统"
authors = ["TartanLog Team"]
license = "MIT"
repository = "https://github.com/your-org/tartan-log"
edition = "2021"
rust-version = "1.70"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "tartanlog_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
# 核心框架
tauri = { version = "2", features = ["macos-private-api"] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"

# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# 数据库相关
sqlx = { version = "0.8", features = [
    "runtime-tokio",
    "tls-rustls-ring-native-roots",
    "mssql",
    "sqlite",
    "chrono",
    "uuid",
    "macros",
    "migrate"
] }
sea-orm = { version = "1.1", features = [
    "sqlx-mssql",
    "sqlx-sqlite",
    "runtime-tokio-rustls",
    "macros",
    "with-chrono",
    "with-uuid"
] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID支持
uuid = { version = "1.0", features = ["v4", "serde"] }

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 配置管理
config = "0.14"
toml = "0.8"

# 网络和HTTP
reqwest = { version = "0.12", features = ["json", "rustls-tls"] }

# 实用工具
once_cell = "1.19"
parking_lot = "0.12"

