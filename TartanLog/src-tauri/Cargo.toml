[package]
name = "tartanlog"
version = "1.0.0"
description = "达坦实时Gamma成像系统"
authors = ["TartanLog Team"]
license = "MIT"
repository = "https://github.com/your-org/tartan-log"
edition = "2021"
rust-version = "1.70"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "tartanlog_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
# 核心框架
tauri = { version = "2", features = ["macos-private-api"] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"

# 异步运行时（优化：只启用需要的功能）
tokio = { version = "1.0", features = [
    "rt-multi-thread",
    "net",
    "time",
    "sync",
    "macros"
] }

# 数据库相关（优化：移除Sea-ORM，专注SQLx）
sqlx = { version = "0.8", features = [
    "runtime-tokio-rustls",
    "mssql",
    "sqlite",
    "chrono",
    "uuid",
    "macros",
    "migrate"
] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID支持
uuid = { version = "1.0", features = ["v4", "serde"] }

# 错误处理（优化：使用eyre替代anyhow）
eyre = "0.6"
thiserror = "1.0"

# 日志（优化：简化tracing配置）
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["fmt", "env-filter"], default-features = false }

# 配置管理（优化：使用figment）
figment = { version = "0.10", features = ["toml", "env"] }

# 网络和HTTP（优化：使用轻量级ureq）
ureq = { version = "2.9", features = ["json", "tls"] }

# 实用工具（优化：移除once_cell，使用std::sync::LazyLock）
parking_lot = "0.12"
crossbeam = "0.8"  # 无锁数据结构，适合实时数据

# 性能优化
mimalloc = { version = "0.1", default-features = false }

# 序列化优化（用于实时数据传输）
rmp-serde = "1.1"  # MessagePack，比JSON更快

[features]
# 默认功能
default = ["sqlite", "mimalloc"]

# 数据库功能（优化：移除sea-orm依赖）
sqlite = ["sqlx/sqlite"]
mssql = ["sqlx/mssql"]

# 开发功能
mock = []
dev = ["mock"]

# 性能功能
mimalloc = ["dep:mimalloc"]

# 性能优化配置
[profile.release]
panic = "abort"
codegen-units = 1
lto = true
incremental = false
opt-level = "z"
strip = true

# 开发时优化sqlx-macros构建性能
[profile.dev.package.sqlx-macros]
opt-level = 3

