# MSSQL配置示例

## 🔧 支持的连接方式

### 1. SQL Server身份验证 (开发环境)

**配置文件**: `config/development.toml`
```toml
[database]
# 你的开发环境配置
primary_url = "mssql://sa:Clash234!!@*************:1433/MEZINTELMWD"
local_url = "sqlite:./data/dev.db"
max_connections = 5
```

**环境变量覆盖**:
```bash
export TARTANLOG_DATABASE__PRIMARY_URL="mssql://sa:Clash234!!@*************:1433/MEZINTELMWD"
```

### 2. Windows集成身份验证 (客户环境)

**配置文件**: `config/client.toml`
```toml
[database]
# 客户环境 - 对应C#中的连接字符串
# "Data Source=.\\SQLEXPRESS;Initial Catalog =MEZINTELMWD;Integrated Security=true;"
primary_url = "mssql://localhost\\SQLEXPRESS/MEZINTELMWD?trusted_connection=true"
local_url = "sqlite:./data/client.db"
max_connections = 10
```

**其他Windows集成身份验证示例**:
```toml
# 使用默认实例和端口
primary_url = "mssql://localhost:1433/MEZINTELMWD?trusted_connection=true"

# 使用远程服务器
primary_url = "mssql://server01\\SQLEXPRESS/MEZINTELMWD?trusted_connection=true"

# 使用完全限定域名
primary_url = "mssql://sqlserver.company.com:1433/MEZINTELMWD?trusted_connection=true"
```

## 🚀 使用方法

### 开发环境启动
```bash
# 设置开发环境
export TARTANLOG_ENV=development

# 启用MSSQL功能并运行
cargo run --features mssql
```

### 客户环境启动
```bash
# 设置客户环境
export TARTANLOG_ENV=client

# 启用MSSQL功能并运行
cargo run --features mssql
```

### 生产环境启动
```bash
# 设置生产环境
export TARTANLOG_ENV=production

# 编译发布版本
cargo build --release --features mssql

# 运行
./target/release/tartanlog
```

## 🔍 连接测试

### 在代码中测试连接
```rust
use crate::database::DatabaseManager;
use crate::config::AppConfig;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化配置
    AppConfig::init()?;
    let config = AppConfig::global();
    
    // 创建数据库管理器
    let mut db_manager = DatabaseManager::new();
    
    // 测试MSSQL连接
    match db_manager.connect_mssql(&config.database().primary_url).await {
        Ok(_) => println!("✅ MSSQL连接成功"),
        Err(e) => println!("❌ MSSQL连接失败: {}", e),
    }
    
    // 测试SQLite连接
    match db_manager.connect_sqlite(&config.database().local_url).await {
        Ok(_) => println!("✅ SQLite连接成功"),
        Err(e) => println!("❌ SQLite连接失败: {}", e),
    }
    
    // 健康检查
    let health = db_manager.health_check().await?;
    println!("数据库状态: {:?}", health);
    
    Ok(())
}
```

## 🛠️ 故障排除

### 常见问题

1. **Windows集成身份验证失败**
   - 确保运行程序的用户有数据库访问权限
   - 检查SQL Server是否启用了Windows身份验证
   - 确保在Windows系统上运行

2. **命名实例连接失败**
   - 确保SQL Server Browser服务正在运行
   - 检查防火墙设置
   - 验证实例名称是否正确

3. **网络连接问题**
   - 检查IP地址和端口是否正确
   - 确保SQL Server允许远程连接
   - 验证网络连通性

### 调试日志

启用详细日志来诊断连接问题：
```bash
export TARTANLOG_LOGGING__LEVEL=debug
cargo run --features mssql
```

## 📋 配置检查清单

### 开发环境
- [ ] 确认*************服务器可访问
- [ ] 验证sa用户密码: Clash234!!
- [ ] 测试端口1433连通性
- [ ] 确认MEZINTELMWD数据库存在

### 客户环境
- [ ] 确认SQLEXPRESS实例正在运行
- [ ] 验证Windows用户有数据库权限
- [ ] 检查SQL Server身份验证模式
- [ ] 确认防火墙允许连接

### 生产环境
- [ ] 使用安全的连接字符串
- [ ] 启用SSL/TLS加密
- [ ] 配置适当的连接池大小
- [ ] 设置合理的超时时间
