# 苹果风格设计指南

## 🎨 设计概览

本项目已完全重新设计，遵循苹果官方设计指南，实现了现代化的苹果风格界面。

## 🌈 颜色系统

### 苹果系统颜色
- **蓝色**: `#007AFF` (浅色) / `#0A84FF` (深色)
- **绿色**: `#34C759` (浅色) / `#30D158` (深色)  
- **橙色**: `#FF9500` (浅色) / `#FF9F0A` (深色)
- **红色**: `#FF3B30` (浅色) / `#FF453A` (深色)
- **紫色**: `#AF52DE` (浅色) / `#BF5AF2` (深色)
- **粉色**: `#FF2D92` (浅色) / `#FF375F` (深色)
- **青色**: `#5AC8FA` (浅色) / `#64D2FF` (深色)
- **靛蓝**: `#5856D6` (浅色) / `#5E5CE6` (深色)

### 灰度系统
- **Gray 50**: `#F2F2F7`
- **Gray 100**: `#E5E5EA`
- **Gray 200**: `#D1D1D6`
- **Gray 300**: `#C7C7CC`
- **Gray 400**: `#AEAEB2`
- **Gray 500**: `#8E8E93`
- **Gray 600**: `#636366`
- **Gray 700**: `#48484A`
- **Gray 800**: `#3A3A3C`
- **Gray 900**: `#2C2C2E`
- **Gray 950**: `#1C1C1E`

## 🔤 字体系统

使用苹果官方字体栈：
```css
font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif
```

### 字体特性
- **字体平滑**: `-webkit-font-smoothing: antialiased`
- **字体特性**: `font-feature-settings: "kern" 1, "liga" 1, "calt" 1`

## 🌙 深色模式支持

### 自动主题切换
- 检测系统偏好设置
- 本地存储用户选择
- 平滑过渡动画

### 主题切换组件
位置：右上角主题切换按钮
功能：一键切换浅色/深色模式

## 🎯 设计特色

### 1. 现代化卡片设计
- **圆角**: `border-radius: 1rem` (16px)
- **阴影**: 轻微阴影效果
- **渐变头部**: 每个卡片使用不同的苹果系统颜色渐变

### 2. 按钮设计
- **圆角**: `border-radius: 0.75rem` (12px)
- **过渡动画**: `transition-all duration-200`
- **悬停效果**: 颜色深度变化
- **阴影**: 主要按钮带有阴影效果

### 3. 颜色映射
- **Job设置**: 蓝色渐变 (Apple Blue)
- **Gamma设置**: 绿色渐变 (Apple Green)
- **成像设置**: 橙色渐变 (Apple Orange)
- **实时曲线**: 紫色渐变 (Apple Purple)
- **数据记录**: 青色渐变 (Apple Teal)

### 4. 交互反馈
- **连接状态**: 绿色 (已连接) / 灰色 (未连接)
- **运行状态**: 红色 (停止) / 蓝色 (开始)
- **悬停效果**: 所有可交互元素都有悬停反馈

## 🚀 技术实现

### CSS变量系统
```css
:root {
  --primary: 214 100% 50%; /* Apple Blue Light */
  --destructive: 4 90% 58%; /* Apple Red Light */
  --radius: 0.75rem; /* Apple-style rounded corners */
}

.dark {
  --primary: 214 86% 55%; /* Apple Blue Dark */
  --destructive: 4 90% 63%; /* Apple Red Dark */
}
```

### Tailwind配置
- 添加苹果系统颜色
- 配置苹果字体栈
- 支持深色模式

## 📱 响应式设计

- **移动优先**: 完全响应式布局
- **断点**: 遵循Tailwind CSS断点系统
- **网格布局**: 自适应列数

## ✨ 用户体验

### 视觉层次
1. **主标题**: 大字体 + 图标
2. **卡片标题**: 渐变背景 + 白色文字
3. **内容区域**: 清晰的间距和对比度

### 状态指示
- **连接状态**: 彩色徽章
- **运行状态**: 动态按钮文字和图标
- **数据状态**: 占位符和加载提示

### 交互设计
- **即时反馈**: 所有操作都有视觉反馈
- **平滑动画**: 200ms过渡动画
- **一致性**: 统一的交互模式

## 🎨 设计原则

1. **简洁性**: 遵循苹果的极简设计理念
2. **一致性**: 统一的颜色、字体和间距
3. **可访问性**: 足够的对比度和清晰的视觉层次
4. **现代感**: 使用最新的设计趋势和技术
5. **专业性**: 适合工业应用的严谨设计

## 🔧 自定义指南

如需调整设计，请修改：
1. `tailwind.config.js` - 颜色和字体配置
2. `src/App.css` - CSS变量定义
3. `src/components/ThemeToggle.tsx` - 主题切换逻辑
